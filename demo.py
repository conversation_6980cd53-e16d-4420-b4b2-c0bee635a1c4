#!/usr/bin/env python3
"""
عرض تجريبي سريع لنظام الاستخبارات الاجتماعية
Quick Demo for Social Media Intelligence System
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def print_banner():
    """طباعة شعار النظام"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🔍 نظام الاستخبارات الاجتماعية 🔍                      ║
║                           عرض تجريبي سريع                                 ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🌐 تحليل المنصات الاجتماعية  🕵️ جمع المعلومات  🤖 الذكاء الاصطناعي    ║
║  🔒 الأمان والتشفير         ⚖️ الامتثال القانوني   🎓 الوضع التعليمي     ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_step(step_num, title, description=""):
    """طباعة خطوة في العرض التجريبي"""
    print(f"\n🔹 الخطوة {step_num}: {title}")
    if description:
        print(f"   {description}")
    print("-" * 50)

def simulate_loading(message, duration=2):
    """محاكاة تحميل مع نقاط متحركة"""
    print(f"🔄 {message}", end="", flush=True)
    for _ in range(duration * 2):
        print(".", end="", flush=True)
        time.sleep(0.5)
    print(" ✅")

async def demo_profile_analysis():
    """عرض تجريبي لتحليل الملف الشخصي"""
    print_step(1, "تحليل ملف شخصي على Instagram", "تحليل شامل مع AI و OSINT")
    
    try:
        from social_accounts_standalone import EnhancedSocialMediaIntelligence
        
        simulate_loading("إنشاء نظام الاستخبارات", 2)
        intelligence = EnhancedSocialMediaIntelligence()
        
        simulate_loading("بدء تشغيل النظام", 1)
        intelligence.start_social_media_system()
        
        simulate_loading("تحليل الملف الشخصي @demo_user", 3)
        
        # تحليل تجريبي (محاكاة آمنة)
        result = await intelligence.analyze_profile(
            platform="instagram",
            username_or_url="demo_user",
            use_real_scraping=False,  # محاكاة للأمان
            include_osint=True,
            include_ai_analysis=True
        )
        
        if result and 'error' not in result:
            print("✅ تم التحليل بنجاح!")
            print(f"📊 درجة المخاطر: {result.get('risk_score', 0.0):.2f}/1.0")
            print(f"🔧 الطرق المستخدمة: {', '.join(result.get('methods_used', []))}")
            
            # عرض بعض النتائج
            profile_data = result.get('profile_data', {})
            if profile_data:
                print(f"👥 المتابعون: {profile_data.get('follower_count', 'غير متوفر'):,}")
                print(f"✅ محقق: {'نعم' if profile_data.get('verified') else 'لا'}")
            
            # تحليل AI
            ai_analysis = result.get('ai_analysis', {})
            if ai_analysis:
                authenticity = ai_analysis.get('authenticity_assessment', {})
                if authenticity:
                    score = authenticity.get('overall_authenticity', 0.0)
                    print(f"🤖 درجة الأصالة: {score:.2f}/1.0")
        
        intelligence.stop_social_media_system()
        
    except ImportError:
        print("⚠️ الوحدات المطلوبة غير متوفرة - عرض محاكاة")
        simulate_loading("تحليل الملف الشخصي", 2)
        print("✅ تم التحليل بنجاح! (محاكاة)")
        print("📊 درجة المخاطر: 0.35/1.0")
        print("👥 المتابعون: 15,420")
        print("✅ محقق: لا")
        print("🤖 درجة الأصالة: 0.78/1.0")

def demo_osint_investigation():
    """عرض تجريبي لتحقيق OSINT"""
    print_step(2, "تحقيق OSINT لإيميل", "فحص التسريبات والسمعة")
    
    try:
        from osint_collector import RealOSINTCollector
        
        simulate_loading("إعداد أدوات OSINT", 1)
        simulate_loading("فحص الإيميل <EMAIL>", 3)
        
        print("✅ تم التحقيق بنجاح!")
        print("📧 الإيميل: <EMAIL>")
        print("🚨 التسريبات المكتشفة: 2")
        print("⚠️ مستوى المخاطر: متوسط")
        print("🔍 السمعة: نظيف")
        print("📊 المصادر: HaveIBeenPwned, EmailRep")
        
    except ImportError:
        simulate_loading("فحص الإيميل", 2)
        print("✅ تم التحقيق بنجاح! (محاكاة)")
        print("📧 الإيميل: <EMAIL>")
        print("🚨 التسريبات المكتشفة: 2")
        print("⚠️ مستوى المخاطر: متوسط")

def demo_ai_analysis():
    """عرض تجريبي لتحليل AI"""
    print_step(3, "تحليل المحتوى بالذكاء الاصطناعي", "تحليل المشاعر والسلوك")
    
    try:
        from ai_analyzer import AIContentAnalyzer
        
        simulate_loading("تحميل نماذج الذكاء الاصطناعي", 2)
        
        analyzer = AIContentAnalyzer()
        text = "أحب هذا المنتج الرائع! إنه يعمل بشكل مثالي."
        
        simulate_loading("تحليل النص", 2)
        
        # تحليل المشاعر
        sentiment = analyzer.analyze_sentiment_textblob(text)
        
        print("✅ تم التحليل بنجاح!")
        print(f"📝 النص: '{text}'")
        if sentiment and 'error' not in sentiment:
            print(f"😊 المشاعر: {sentiment.get('sentiment', 'غير معروف')}")
            print(f"📊 القطبية: {sentiment.get('polarity', 0.0):.2f}")
            print(f"🎭 الذاتية: {sentiment.get('subjectivity', 0.0):.2f}")
        
    except ImportError:
        simulate_loading("تحليل النص", 2)
        print("✅ تم التحليل بنجاح! (محاكاة)")
        print("📝 النص: 'أحب هذا المنتج الرائع!'")
        print("😊 المشاعر: إيجابي")
        print("📊 القطبية: 0.85")

async def demo_account_creation():
    """عرض تجريبي لإنشاء حساب تعليمي"""
    print_step(4, "إنشاء حساب تعليمي", "محاكاة آمنة لإنشاء حساب")
    
    # عرض تحذير قانوني
    print("⚠️ تحذير قانوني: هذا عرض تعليمي فقط!")
    print("   لن يتم إنشاء حساب حقيقي على أي منصة")
    
    try:
        from social_accounts_standalone import EnhancedSocialMediaIntelligence
        
        simulate_loading("إعداد أدوات الإنشاء", 1)
        intelligence = EnhancedSocialMediaIntelligence()
        intelligence.start_social_media_system()
        
        simulate_loading("إنشاء هوية مزيفة", 2)
        simulate_loading("إنشاء حساب تعليمي على Instagram", 3)
        
        # إنشاء حساب تعليمي
        result = await intelligence.create_fake_account(
            platform="instagram",
            account_type="educational",
            educational_mode=True
        )
        
        if result and 'error' not in result:
            print("✅ تم إنشاء الحساب التعليمي بنجاح!")
            print(f"👤 اسم المستخدم: {result.get('username', 'غير متوفر')}")
            print(f"📧 الإيميل: {result.get('email', 'غير متوفر')}")
            print(f"🎓 وضع تعليمي: {result.get('educational_mode', False)}")
            print("📝 ملاحظة: هذا حساب محاكاة فقط")
        
        intelligence.stop_social_media_system()
        
    except ImportError:
        simulate_loading("إنشاء حساب تعليمي", 2)
        print("✅ تم إنشاء الحساب التعليمي بنجاح! (محاكاة)")
        print("👤 اسم المستخدم: demo_user_2024")
        print("📧 الإيميل: <EMAIL>")
        print("🎓 وضع تعليمي: نعم")

def demo_security_features():
    """عرض تجريبي لميزات الأمان"""
    print_step(5, "ميزات الأمان والتشفير", "حماية البيانات والامتثال")
    
    try:
        from security_manager import SecurityManager
        
        simulate_loading("إعداد نظام الأمان", 1)
        security = SecurityManager()
        
        simulate_loading("تشفير البيانات الحساسة", 2)
        
        # تشفير تجريبي
        test_data = {"password": "secret123", "email": "<EMAIL>"}
        encrypted = security.encrypt_data(test_data)
        
        print("✅ تم تشفير البيانات بنجاح!")
        print("🔐 خوارزمية التشفير: AES-256")
        print("📝 سجل المراجعة: مفعل")
        print("⚖️ الامتثال القانوني: مفعل")
        print("🎓 الوضع التعليمي: مفعل")
        
        # عرض حالة الأمان
        status = security.get_security_status()
        print(f"🔒 التشفير: {'✅' if status['encryption_enabled'] else '❌'}")
        print(f"📊 سجل المراجعة: {'✅' if status['audit_logging_enabled'] else '❌'}")
        
    except ImportError:
        simulate_loading("إعداد الأمان", 2)
        print("✅ تم إعداد الأمان بنجاح! (محاكاة)")
        print("🔐 خوارزمية التشفير: AES-256")
        print("📝 سجل المراجعة: مفعل")

def demo_system_capabilities():
    """عرض قدرات النظام"""
    print_step(6, "قدرات النظام", "عرض جميع الميزات المتاحة")
    
    try:
        from social_accounts_standalone import EnhancedSocialMediaIntelligence
        
        intelligence = EnhancedSocialMediaIntelligence()
        
        print("🔧 القدرات المتاحة:")
        capabilities = [
            ("تحليل الملفات الشخصية الحقيقي", "✅"),
            ("جمع معلومات OSINT", "✅"),
            ("تحليل المحتوى بالذكاء الاصطناعي", "✅"),
            ("إنشاء حسابات تعليمية", "✅"),
            ("التشفير والأمان", "✅"),
            ("سجل المراجعة", "✅"),
            ("الامتثال القانوني", "✅")
        ]
        
        for capability, status in capabilities:
            print(f"   {status} {capability}")
        
        print("\n🌐 المنصات المدعومة:")
        platforms = ["Facebook", "Instagram", "Twitter", "LinkedIn"]
        for platform in platforms:
            print(f"   ✅ {platform}")
        
    except ImportError:
        print("🔧 القدرات المتاحة: (محاكاة)")
        print("   ✅ تحليل الملفات الشخصية")
        print("   ✅ جمع معلومات OSINT")
        print("   ✅ تحليل الذكاء الاصطناعي")

async def run_full_demo():
    """تشغيل العرض التجريبي الكامل"""
    print_banner()
    
    print("🎬 مرحباً بك في العرض التجريبي لنظام الاستخبارات الاجتماعية!")
    print("   سيتم عرض جميع الميزات الرئيسية في 6 خطوات")
    print("   العرض آمن ولا يستخدم بيانات حقيقية")
    
    input("\n📱 اضغط Enter لبدء العرض التجريبي...")
    
    # تشغيل جميع العروض التجريبية
    await demo_profile_analysis()
    input("\n⏭️ اضغط Enter للمتابعة...")
    
    demo_osint_investigation()
    input("\n⏭️ اضغط Enter للمتابعة...")
    
    demo_ai_analysis()
    input("\n⏭️ اضغط Enter للمتابعة...")
    
    await demo_account_creation()
    input("\n⏭️ اضغط Enter للمتابعة...")
    
    demo_security_features()
    input("\n⏭️ اضغط Enter للمتابعة...")
    
    demo_system_capabilities()
    
    # خاتمة العرض
    print("\n" + "="*60)
    print("🎉 انتهى العرض التجريبي بنجاح!")
    print("="*60)
    
    print("\n💡 الخطوات التالية:")
    print("   1. أضف مفاتيح API للحصول على نتائج حقيقية")
    print("   2. اقرأ دليل الاستخدام: USAGE_GUIDE.md")
    print("   3. جرب الأمثلة العملية: python examples.py")
    print("   4. استخدم القائمة التفاعلية: python run.py")
    
    print("\n⚖️ تذكير قانوني:")
    print("   استخدم هذا النظام للأغراض التعليمية والبحثية فقط")
    print("   احصل على إذن قبل تحليل أي حسابات حقيقية")
    print("   احترم قوانين الخصوصية وشروط الخدمة")
    
    print("\n🚀 ابدأ الاستخدام الآن: python run.py")

if __name__ == "__main__":
    try:
        asyncio.run(run_full_demo())
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف العرض التجريبي")
    except Exception as e:
        print(f"\n❌ خطأ في العرض التجريبي: {e}")
        print("💡 جرب: python run.py للاستخدام العادي")

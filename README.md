# 🔍 Enhanced Social Media Intelligence System

## نظام الاستخبارات المحسن لوسائل التواصل الاجتماعي

A comprehensive, real-world social media intelligence and OSINT collection system with AI-powered analysis, security features, and legal compliance.

## ⚠️ **IMPORTANT LEGAL NOTICE**

**This tool is designed for EDUCATIONAL and RESEARCH purposes only.**

### ✅ **Permitted Uses:**
- Academic research with proper institutional approval
- Security research and vulnerability assessment
- Authorized penetration testing
- Educational demonstrations and training

### ❌ **Prohibited Uses:**
- Unauthorized access to accounts or systems
- Identity theft or impersonation
- Harassment, stalking, or malicious activities
- Violation of platform terms of service
- Any illegal activities under applicable law

**By using this software, you acknowledge that you have proper authorization and will comply with all applicable laws.**

## 🚀 **Features**

### 🔍 **Real OSINT Collection**
- **Email Intelligence**: Hunter.io, HaveIBeenPwned, EmailRep.io integration
- **Phone Number Analysis**: Numverify API and local phonenumbers library
- **Username Availability**: Cross-platform username checking
- **Data Breach Detection**: Real-time breach exposure analysis

### 🌐 **Real Web Scraping**
- **Selenium Integration**: Undetected Chrome driver for realistic scraping
- **Multi-Platform Support**: Facebook, Instagram, Twitter, LinkedIn
- **Anti-Detection**: Human-like behavior simulation
- **Rate Limiting**: Respectful scraping with proper delays

### 🤖 **AI-Powered Analysis**
- **Sentiment Analysis**: Transformers and TextBlob integration
- **Content Analysis**: Comprehensive text and behavioral analysis
- **Authenticity Assessment**: AI-based authenticity scoring
- **Behavioral Patterns**: Temporal and engagement pattern analysis

### 🔒 **Security & Compliance**
- **AES Encryption**: Secure data storage with Fernet encryption
- **Audit Logging**: Comprehensive activity logging
- **Legal Compliance**: GDPR-compliant data handling
- **Educational Mode**: Safe demonstration environment

### 🎯 **Fake Account Creation (Educational)**
- **Automated Registration**: Selenium-based account creation
- **CAPTCHA Solving**: 2captcha integration with manual fallback
- **Temporary Emails**: Multiple temporary email services
- **Educational Warnings**: Clear legal and ethical guidelines

## 📦 **Installation**

### Prerequisites
```bash
# Python 3.8+ required
python --version

# Install Chrome/Chromium for Selenium
# Ubuntu/Debian:
sudo apt-get install chromium-browser

# macOS:
brew install chromium

# Windows: Download Chrome from Google
```

### Install Dependencies
```bash
# Clone the repository
git clone <repository-url>
cd social_media_intelligence

# Install Python dependencies
pip install -r requirements.txt

# Download NLTK data (for AI analysis)
python -c "import nltk; nltk.download('punkt'); nltk.download('vader_lexicon')"
```

### API Keys Configuration
Create a `.env` file in the project root:

```bash
# OSINT APIs
HUNTER_IO_API_KEY=your_hunter_io_key
HIBP_API_KEY=your_haveibeenpwned_key
EMAILREP_API_KEY=your_emailrep_key
NUMVERIFY_API_KEY=your_numverify_key

# CAPTCHA Solving
TWOCAPTCHA_API_KEY=your_2captcha_key

# Temporary Email Services
TEMPMAIL_API_KEY=your_tempmail_key
```

## 🖥️ **Usage**

### Command Line Interface
```bash
# Run the enhanced CLI
python cli_interface.py

# Or run the main module directly
python social_accounts_standalone.py
```

### Programmatic Usage
```python
import asyncio
from social_accounts_standalone import EnhancedSocialMediaIntelligence

async def main():
    # Initialize system
    intelligence = EnhancedSocialMediaIntelligence()
    intelligence.start_social_media_system()
    
    # Analyze a profile
    result = await intelligence.analyze_profile(
        platform="instagram",
        username_or_url="example_user",
        use_real_scraping=True,
        include_osint=True,
        include_ai_analysis=True
    )
    
    print(f"Risk Score: {result.get('risk_score', 0.0)}")
    print(f"Methods Used: {result.get('methods_used', [])}")
    
    # Cleanup
    intelligence.stop_social_media_system()

# Run async main
asyncio.run(main())
```

## 📊 **Analysis Features**

### Profile Analysis
- **Basic Information**: Followers, posts, verification status
- **Content Analysis**: Bio sentiment, posting patterns
- **Risk Assessment**: Multi-factor risk scoring
- **Cross-Platform Presence**: Username availability checking

### OSINT Investigation
- **Email Analysis**: Breach exposure, reputation, domain intelligence
- **Phone Analysis**: Validation, carrier information, geographic data
- **Username Intelligence**: Platform availability and similarity analysis

### AI Analysis
- **Sentiment Analysis**: Multiple model support (Transformers, TextBlob)
- **Behavioral Analysis**: Posting patterns, engagement analysis
- **Authenticity Scoring**: AI-based authenticity assessment
- **Content Classification**: Topic detection and style analysis

## 🔧 **Configuration**

### Security Settings
```python
# config.py
SECURITY_CONFIG = {
    'encryption_algorithm': 'AES',
    'key_length': 256,
    'audit_log_retention': 90,  # days
    'max_login_attempts': 3
}
```

### Platform Settings
```python
# config.py
PLATFORM_CONFIGS = {
    'instagram': {
        'rate_limit': 3,  # seconds between requests
        'max_retries': 3,
        'selectors': {
            'username': 'input[name="username"]',
            'followers': 'a[href*="/followers/"] span'
        }
    }
}
```

## 📁 **Project Structure**

```
social_media_intelligence/
├── social_accounts_standalone.py  # Main enhanced system
├── web_scraper.py                 # Real web scraping module
├── osint_collector.py             # OSINT collection APIs
├── ai_analyzer.py                 # AI content analysis
├── fake_account_creator.py        # Account creation (educational)
├── security_manager.py            # Security and encryption
├── cli_interface.py               # Command line interface
├── config.py                      # Configuration settings
├── requirements.txt               # Python dependencies
├── README.md                      # This file
├── data/                          # Data storage directory
├── logs/                          # Log files
└── cache/                         # Temporary cache
```

## 🛡️ **Security Features**

### Data Protection
- **AES-256 Encryption**: All sensitive data encrypted at rest
- **Secure Key Management**: Encryption keys stored securely
- **Data Anonymization**: Personal data anonymized when possible
- **Automatic Cleanup**: Old data automatically purged

### Audit & Compliance
- **Activity Logging**: All actions logged with timestamps
- **Security Events**: Suspicious activity detection
- **Legal Consent**: User consent tracking
- **GDPR Compliance**: Right to deletion and data portability

## 🧪 **Testing**

### Run Tests
```bash
# Run all tests
python -m pytest test_social_accounts.py -v

# Run specific test categories
python -m pytest test_social_accounts.py::test_profile_analysis -v
python -m pytest test_social_accounts.py::test_osint_collection -v
python -m pytest test_social_accounts.py::test_ai_analysis -v
```

### Educational Mode Testing
```bash
# Run in safe educational mode
python social_accounts_standalone.py --educational-mode

# Test with simulated data only
python cli_interface.py --demo-mode
```

## 📈 **Performance Optimization**

### Recommended Settings
- **Concurrent Requests**: Max 5 simultaneous requests
- **Rate Limiting**: Respect platform rate limits
- **Caching**: Enable result caching for repeated queries
- **Resource Management**: Proper cleanup of browser instances

### Monitoring
- **System Status**: Real-time capability monitoring
- **Performance Metrics**: Request timing and success rates
- **Resource Usage**: Memory and CPU monitoring
- **Error Tracking**: Comprehensive error logging

## 🤝 **Contributing**

### Development Guidelines
1. **Legal Compliance**: Ensure all contributions comply with legal requirements
2. **Security First**: Security considerations in all code changes
3. **Documentation**: Comprehensive documentation for new features
4. **Testing**: Unit tests for all new functionality

### Code Style
```bash
# Format code
black *.py

# Lint code
flake8 *.py

# Type checking
mypy *.py
```

## 📄 **License**

This project is licensed under the MIT License with additional ethical use restrictions. See the LICENSE file for details.

## ⚖️ **Ethical Guidelines**

### Research Ethics
- Obtain proper institutional approval for research
- Respect privacy and data protection laws
- Use minimal data necessary for research objectives
- Anonymize and protect collected data

### Responsible Disclosure
- Report security vulnerabilities responsibly
- Do not exploit discovered vulnerabilities
- Coordinate with platform security teams
- Follow responsible disclosure timelines

## 📞 **Support**

### Documentation
- **Wiki**: Comprehensive documentation and tutorials
- **Examples**: Real-world usage examples
- **FAQ**: Frequently asked questions
- **Troubleshooting**: Common issues and solutions

### Community
- **Issues**: Report bugs and request features
- **Discussions**: Community discussions and help
- **Security**: Report security issues privately

## 🔄 **Updates**

### Version History
- **v2.0.0**: Enhanced real capabilities, AI analysis, security features
- **v1.0.0**: Basic simulation and educational features

### Roadmap
- **Web GUI**: Browser-based interface
- **API Server**: REST API for integration
- **Advanced AI**: More sophisticated analysis models
- **Platform Expansion**: Additional social media platforms

---

**Remember: Use this tool responsibly and ethically. Always obtain proper authorization before conducting any intelligence gathering activities.**

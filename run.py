#!/usr/bin/env python3
"""
Quick Start Script for Enhanced Social Media Intelligence System
Provides easy access to all system features
"""

import asyncio
import sys
import os
from typing import Optional

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from social_accounts_standalone import EnhancedSocialMediaIntelligence
    from cli_interface import SocialIntelligenceCLI
    ENHANCED_AVAILABLE = True
except ImportError as e:
    print(f"Enhanced modules not available: {e}")
    ENHANCED_AVAILABLE = False

def print_banner():
    """Print application banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    SOCIAL MEDIA INTELLIGENCE SYSTEM                         ║
║                           Quick Start Menu                                  ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🔍 Real OSINT Collection    🤖 AI-Powered Analysis    🔒 Security Features  ║
║  🌐 Multi-Platform Support  📊 Comprehensive Reports  ⚖️  Legal Compliance  ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_menu():
    """Print main menu options"""
    print("\n🚀 Quick Start Options:")
    print("=" * 50)
    print("1. 🖥️  Launch Interactive CLI")
    print("2. 🔍 Quick Profile Analysis")
    print("3. 🕵️  OSINT Investigation")
    print("4. 🤖 AI Content Analysis Demo")
    print("5. 🎓 Educational Account Creation")
    print("6. 📊 System Status & Capabilities")
    print("7. 🧪 Run System Tests")
    print("8. ⚙️  Configuration Setup")
    print("9. 📚 Help & Documentation")
    print("0. ❌ Exit")
    print("=" * 50)

async def quick_profile_analysis():
    """Quick profile analysis demo"""
    print("\n🔍 Quick Profile Analysis")
    print("-" * 30)
    
    if not ENHANCED_AVAILABLE:
        print("❌ Enhanced modules not available")
        return
    
    platform = input("Platform (instagram/facebook/twitter/linkedin): ").strip().lower()
    if platform not in ['instagram', 'facebook', 'twitter', 'linkedin']:
        print("❌ Invalid platform")
        return
    
    username = input("Username: ").strip()
    if not username:
        print("❌ Username required")
        return
    
    print(f"\n🔄 Analyzing {platform} profile: {username}")
    print("This may take a few moments...")
    
    try:
        intelligence = EnhancedSocialMediaIntelligence()
        intelligence.start_social_media_system()
        
        result = await intelligence.analyze_profile(
            platform, username,
            use_real_scraping=False,  # Use simulation for demo
            include_osint=True,
            include_ai_analysis=True
        )
        
        if result and 'error' not in result:
            print(f"\n✅ Analysis completed!")
            print(f"📊 Risk Score: {result.get('risk_score', 0.0):.2f}")
            print(f"🔧 Methods Used: {', '.join(result.get('methods_used', []))}")
            
            profile_data = result.get('profile_data', {})
            if profile_data:
                print(f"👥 Followers: {profile_data.get('follower_count', 'N/A')}")
                print(f"✅ Verified: {profile_data.get('verified', 'N/A')}")
            
            ai_analysis = result.get('ai_analysis', {})
            if ai_analysis:
                authenticity = ai_analysis.get('authenticity_assessment', {})
                print(f"🤖 AI Authenticity: {authenticity.get('overall_authenticity', 0.0):.2f}")
        else:
            print(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")
        
        intelligence.stop_social_media_system()
        
    except Exception as e:
        print(f"❌ Error: {e}")

async def osint_investigation():
    """Quick OSINT investigation demo"""
    print("\n🕵️ OSINT Investigation")
    print("-" * 25)
    
    if not ENHANCED_AVAILABLE:
        print("❌ Enhanced modules not available")
        return
    
    investigation_type = input("Type (email/phone/username): ").strip().lower()
    if investigation_type not in ['email', 'phone', 'username']:
        print("❌ Invalid investigation type")
        return
    
    target = input(f"Enter {investigation_type}: ").strip()
    if not target:
        print("❌ Target required")
        return
    
    print(f"\n🔄 Investigating {investigation_type}: {target}")
    print("This may take a few moments...")
    
    try:
        intelligence = EnhancedSocialMediaIntelligence()
        intelligence.start_social_media_system()
        
        if intelligence.osint_collector:
            if investigation_type == "email":
                result = await intelligence.osint_collector.comprehensive_email_osint(target)
            elif investigation_type == "phone":
                result = await intelligence.osint_collector.comprehensive_phone_osint(target)
            else:
                result = await intelligence.osint_collector.check_username_availability(target)
            
            if result and 'error' not in result:
                print(f"\n✅ Investigation completed!")
                print(f"📊 Sources used: {result.get('sources', [])}")
                
                if investigation_type == "email":
                    breaches = result.get('breaches', {})
                    if breaches:
                        print(f"🚨 Data breaches: {breaches.get('total_breaches', 0)}")
                        print(f"⚠️  Risk level: {breaches.get('risk_level', 'unknown')}")
                
                elif investigation_type == "username":
                    platforms_found = result.get('platforms_found', 0)
                    print(f"🌐 Platforms found: {platforms_found}")
            else:
                print(f"❌ Investigation failed: {result.get('error', 'Unknown error')}")
        else:
            print("❌ OSINT collector not available")
        
        intelligence.stop_social_media_system()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def ai_content_demo():
    """AI content analysis demo"""
    print("\n🤖 AI Content Analysis Demo")
    print("-" * 30)
    
    if not ENHANCED_AVAILABLE:
        print("❌ Enhanced modules not available")
        return
    
    text = input("Enter text to analyze: ").strip()
    if not text:
        print("❌ Text required")
        return
    
    print(f"\n🔄 Analyzing content...")
    
    try:
        intelligence = EnhancedSocialMediaIntelligence()
        
        if intelligence.ai_analyzer:
            # Sentiment analysis
            sentiment = intelligence.ai_analyzer.analyze_sentiment_textblob(text)
            print(f"\n✅ Analysis completed!")
            print(f"😊 Sentiment: {sentiment.get('sentiment', 'unknown')}")
            print(f"📊 Polarity: {sentiment.get('polarity', 0.0):.2f}")
            print(f"🎭 Subjectivity: {sentiment.get('subjectivity', 0.0):.2f}")
            
            # Text statistics
            stats = intelligence.ai_analyzer.extract_text_statistics(text)
            print(f"📝 Word count: {stats.get('word_count', 0)}")
            print(f"📏 Character count: {stats.get('character_count', 0)}")
            print(f"❗ Exclamation marks: {stats.get('exclamation_count', 0)}")
        else:
            print("❌ AI analyzer not available")
        
    except Exception as e:
        print(f"❌ Error: {e}")

async def educational_account_demo():
    """Educational account creation demo"""
    print("\n🎓 Educational Account Creation Demo")
    print("-" * 40)
    
    # Show warning
    warning = """
⚠️  EDUCATIONAL PURPOSE ONLY ⚠️

This demonstration shows how fake accounts could be created
for educational and research purposes only.

Creating fake accounts for malicious purposes is illegal.
Only use this for authorized research and testing.
    """
    print(warning)
    
    consent = input("Do you understand and agree? (yes/no): ").strip().lower()
    if consent != 'yes':
        print("❌ Operation cancelled")
        return
    
    if not ENHANCED_AVAILABLE:
        print("❌ Enhanced modules not available")
        return
    
    platform = input("Platform (instagram/facebook/twitter): ").strip().lower()
    if platform not in ['instagram', 'facebook', 'twitter']:
        print("❌ Invalid platform")
        return
    
    print(f"\n🔄 Creating educational account on {platform}...")
    print("This is a simulation for educational purposes.")
    
    try:
        intelligence = EnhancedSocialMediaIntelligence()
        intelligence.start_social_media_system()
        
        result = await intelligence.create_fake_account(
            platform,
            account_type='educational',
            educational_mode=True
        )
        
        if result and 'error' not in result:
            print(f"\n✅ Educational account created!")
            print(f"👤 Username: {result.get('username', 'N/A')}")
            print(f"📧 Email: {result.get('email', 'N/A')}")
            print(f"📊 Status: {result.get('status', 'N/A')}")
            print(f"🎓 Educational Mode: {result.get('educational_mode', False)}")
            print("\n📝 Note: This is a simulated account for educational purposes only.")
        else:
            print(f"❌ Account creation failed: {result.get('error', 'Unknown error')}")
        
        intelligence.stop_social_media_system()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def show_system_status():
    """Show system status and capabilities"""
    print("\n📊 System Status & Capabilities")
    print("-" * 35)
    
    if not ENHANCED_AVAILABLE:
        print("❌ Enhanced modules not available")
        return
    
    try:
        intelligence = EnhancedSocialMediaIntelligence()
        
        print("🔧 System Capabilities:")
        for capability, enabled in intelligence.capabilities.items():
            status = "✅" if enabled else "❌"
            print(f"  {status} {capability.replace('_', ' ').title()}")
        
        print(f"\n🌐 Supported Platforms:")
        for platform, config in intelligence.platforms.items():
            enabled = "✅" if config['enabled'] else "❌"
            real_scraping = "✅" if config.get('real_scraping', False) else "❌"
            print(f"  {enabled} {platform.title()} (Real Scraping: {real_scraping})")
        
        print(f"\n📈 Statistics:")
        for metric, count in intelligence.stats.items():
            print(f"  📊 {metric.replace('_', ' ').title()}: {count}")
        
        # Security status
        if intelligence.security_manager:
            security_status = intelligence.security_manager.get_security_status()
            print(f"\n🔒 Security Status:")
            print(f"  🔐 Encryption: {'✅' if security_status['encryption_enabled'] else '❌'}")
            print(f"  📝 Audit Logging: {'✅' if security_status['audit_logging_enabled'] else '❌'}")
            print(f"  ⚖️  Legal Consent: {'✅' if security_status['legal_consent'] else '❌'}")
            print(f"  🎓 Educational Mode: {'✅' if security_status['educational_mode'] else '❌'}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def run_tests():
    """Run system tests"""
    print("\n🧪 Running System Tests")
    print("-" * 25)
    
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, "-m", "pytest", "test_enhanced_system.py", "-v"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed")
            
    except Exception as e:
        print(f"❌ Error running tests: {e}")

def setup_configuration():
    """Setup configuration"""
    print("\n⚙️ Configuration Setup")
    print("-" * 22)
    
    print("📁 Configuration files:")
    
    # Check .env file
    if os.path.exists('.env'):
        print("  ✅ .env file exists")
    else:
        print("  ❌ .env file missing")
        if os.path.exists('.env.example'):
            create_env = input("Create .env from example? (y/n): ").strip().lower()
            if create_env == 'y':
                import shutil
                shutil.copy('.env.example', '.env')
                print("  ✅ .env file created from example")
                print("  📝 Please edit .env file with your API keys")
    
    # Check data directories
    directories = ['data', 'logs', 'cache', 'profiles']
    for directory in directories:
        if os.path.exists(directory):
            print(f"  ✅ {directory}/ directory exists")
        else:
            os.makedirs(directory, exist_ok=True)
            print(f"  ✅ {directory}/ directory created")
    
    print("\n📋 Next steps:")
    print("1. Edit .env file with your API keys")
    print("2. Review config.py for advanced settings")
    print("3. Run system tests to verify setup")

def show_help():
    """Show help and documentation"""
    print("\n📚 Help & Documentation")
    print("-" * 25)
    
    print("📖 Available Resources:")
    print("  📄 README.md - Comprehensive documentation")
    print("  📋 requirements.txt - Python dependencies")
    print("  ⚙️  config.py - System configuration")
    print("  🔧 .env.example - Environment variables template")
    print("  🧪 test_enhanced_system.py - Test suite")
    
    print("\n🚀 Quick Commands:")
    print("  python cli_interface.py - Launch interactive CLI")
    print("  python social_accounts_standalone.py - Run main system")
    print("  python -m pytest test_enhanced_system.py - Run tests")
    
    print("\n⚖️ Legal Notice:")
    print("  This tool is for EDUCATIONAL and RESEARCH purposes only.")
    print("  Always obtain proper authorization before use.")
    print("  Respect privacy laws and platform terms of service.")
    
    print("\n🔗 Useful Links:")
    print("  Hunter.io API: https://hunter.io/api")
    print("  HaveIBeenPwned API: https://haveibeenpwned.com/API/Key")
    print("  2captcha: https://2captcha.com/")

async def main():
    """Main function"""
    print_banner()
    
    while True:
        try:
            print_menu()
            choice = input("\nSelect option (0-9): ").strip()
            
            if choice == "0":
                print("\n👋 Goodbye!")
                break
            elif choice == "1":
                print("\n🖥️ Launching Interactive CLI...")
                if ENHANCED_AVAILABLE:
                    cli = SocialIntelligenceCLI()
                    await cli.run()
                else:
                    print("❌ Enhanced CLI not available")
            elif choice == "2":
                await quick_profile_analysis()
            elif choice == "3":
                await osint_investigation()
            elif choice == "4":
                ai_content_demo()
            elif choice == "5":
                await educational_account_demo()
            elif choice == "6":
                show_system_status()
            elif choice == "7":
                run_tests()
            elif choice == "8":
                setup_configuration()
            elif choice == "9":
                show_help()
            else:
                print("❌ Invalid option")
            
            input("\nPress Enter to continue...")
            
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    asyncio.run(main())

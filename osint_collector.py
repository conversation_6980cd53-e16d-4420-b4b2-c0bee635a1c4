#!/usr/bin/env python3
"""
Real OSINT Collection Module
Integrates with real APIs for intelligence gathering
"""

import time
import json
import logging
import asyncio
import hashlib
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

try:
    import aiohttp
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    import phonenumbers
    from phonenumbers import geocoder, carrier
    PHONENUMBERS_AVAILABLE = True
except ImportError:
    PHONENUMBERS_AVAILABLE = False

try:
    from email_validator import validate_email, EmailNotValidError
    EMAIL_VALIDATOR_AVAILABLE = True
except ImportError:
    EMAIL_VALIDATOR_AVAILABLE = False

from config import API_KEYS, OSINT_CONFIG

class RealOSINTCollector:
    """Real OSINT collector using actual APIs and services"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = None
        self.rate_limits = {}

        if not REQUESTS_AVAILABLE:
            raise ImportError("requests and aiohttp are required for OSINT collection")

    async def setup_session(self):
        """Setup async HTTP session"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)

    async def close_session(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None

    def rate_limit_check(self, service: str):
        """Check and enforce rate limits"""
        if service in self.rate_limits:
            time_since_last = time.time() - self.rate_limits[service]
            required_delay = OSINT_CONFIG[service]['rate_limit']
            if time_since_last < required_delay:
                time.sleep(required_delay - time_since_last)

        self.rate_limits[service] = time.time()

    async def hunter_io_email_search(self, domain: str) -> Dict[str, Any]:
        """Search for emails using Hunter.io API"""
        try:
            if not API_KEYS['hunter_io']:
                return {'error': 'Hunter.io API key not configured'}

            self.rate_limit_check('hunter_io')

            url = f"{OSINT_CONFIG['hunter_io']['base_url']}/domain-search"
            params = {
                'domain': domain,
                'api_key': API_KEYS['hunter_io'],
                'limit': 100
            }

            await self.setup_session()
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    emails = []
                    if 'data' in data and 'emails' in data['data']:
                        for email_data in data['data']['emails']:
                            emails.append({
                                'email': email_data.get('value'),
                                'first_name': email_data.get('first_name'),
                                'last_name': email_data.get('last_name'),
                                'position': email_data.get('position'),
                                'confidence': email_data.get('confidence'),
                                'sources': email_data.get('sources', []),
                                'verified': email_data.get('verification', {}).get('result') == 'deliverable'
                            })

                    return {
                        'emails': emails,
                        'domain': domain,
                        'total_emails': len(emails),
                        'organization': data.get('data', {}).get('organization'),
                        'source': 'hunter_io'
                    }
                else:
                    return {'error': f'Hunter.io API error: {response.status}'}

        except Exception as e:
            self.logger.error(f"Hunter.io search error: {e}")
            return {'error': str(e)}

    async def haveibeenpwned_check(self, email: str) -> Dict[str, Any]:
        """Check email in data breaches using HaveIBeenPwned API"""
        try:
            if not API_KEYS['haveibeenpwned']:
                return {'error': 'HaveIBeenPwned API key not configured'}

            self.rate_limit_check('haveibeenpwned')

            url = f"{OSINT_CONFIG['haveibeenpwned']['base_url']}/breachedaccount/{email}"
            headers = {
                'hibp-api-key': API_KEYS['haveibeenpwned'],
                'User-Agent': 'Social-Intelligence-Tool'
            }

            await self.setup_session()
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    breaches = await response.json()

                    breach_data = []
                    for breach in breaches:
                        breach_data.append({
                            'name': breach.get('Name'),
                            'title': breach.get('Title'),
                            'domain': breach.get('Domain'),
                            'breach_date': breach.get('BreachDate'),
                            'added_date': breach.get('AddedDate'),
                            'modified_date': breach.get('ModifiedDate'),
                            'pwn_count': breach.get('PwnCount'),
                            'description': breach.get('Description'),
                            'data_classes': breach.get('DataClasses', []),
                            'is_verified': breach.get('IsVerified'),
                            'is_fabricated': breach.get('IsFabricated'),
                            'is_sensitive': breach.get('IsSensitive'),
                            'is_retired': breach.get('IsRetired'),
                            'is_spam_list': breach.get('IsSpamList')
                        })

                    return {
                        'email': email,
                        'breaches': breach_data,
                        'total_breaches': len(breach_data),
                        'risk_level': self.calculate_breach_risk(breach_data),
                        'source': 'haveibeenpwned'
                    }
                elif response.status == 404:
                    return {
                        'email': email,
                        'breaches': [],
                        'total_breaches': 0,
                        'risk_level': 'low',
                        'source': 'haveibeenpwned'
                    }
                else:
                    return {'error': f'HaveIBeenPwned API error: {response.status}'}

        except Exception as e:
            self.logger.error(f"HaveIBeenPwned check error: {e}")
            return {'error': str(e)}

    def calculate_breach_risk(self, breaches: List[Dict]) -> str:
        """Calculate risk level based on breaches"""
        if not breaches:
            return 'low'

        high_risk_count = sum(1 for breach in breaches if breach.get('is_sensitive'))
        recent_breaches = sum(1 for breach in breaches
                            if breach.get('breach_date') and
                            datetime.fromisoformat(breach['breach_date']) > datetime.now() - timedelta(days=365))

        if high_risk_count > 2 or recent_breaches > 3:
            return 'high'
        elif high_risk_count > 0 or recent_breaches > 1:
            return 'medium'
        else:
            return 'low'

    async def emailrep_check(self, email: str) -> Dict[str, Any]:
        """Check email reputation using EmailRep.io"""
        try:
            self.rate_limit_check('emailrep')

            url = f"{OSINT_CONFIG['emailrep']['base_url']}/{email}"
            headers = {'User-Agent': 'Social-Intelligence-Tool'}

            if API_KEYS['emailrep']:
                headers['Key'] = API_KEYS['emailrep']

            await self.setup_session()
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()

                    return {
                        'email': email,
                        'reputation': data.get('reputation'),
                        'suspicious': data.get('suspicious'),
                        'references': data.get('references'),
                        'details': data.get('details', {}),
                        'source': 'emailrep'
                    }
                else:
                    return {'error': f'EmailRep API error: {response.status}'}

        except Exception as e:
            self.logger.error(f"EmailRep check error: {e}")
            return {'error': str(e)}

    async def numverify_phone_check(self, phone: str) -> Dict[str, Any]:
        """Verify phone number using Numverify API"""
        try:
            if not API_KEYS['numverify']:
                return {'error': 'Numverify API key not configured'}

            self.rate_limit_check('numverify')

            url = f"{OSINT_CONFIG['numverify']['base_url']}/validate"
            params = {
                'access_key': API_KEYS['numverify'],
                'number': phone,
                'country_code': '',
                'format': 1
            }

            await self.setup_session()
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    return {
                        'phone': phone,
                        'valid': data.get('valid'),
                        'number': data.get('number'),
                        'local_format': data.get('local_format'),
                        'international_format': data.get('international_format'),
                        'country_prefix': data.get('country_prefix'),
                        'country_code': data.get('country_code'),
                        'country_name': data.get('country_name'),
                        'location': data.get('location'),
                        'carrier': data.get('carrier'),
                        'line_type': data.get('line_type'),
                        'source': 'numverify'
                    }
                else:
                    return {'error': f'Numverify API error: {response.status}'}

        except Exception as e:
            self.logger.error(f"Numverify check error: {e}")
            return {'error': str(e)}

    def analyze_phone_local(self, phone: str) -> Dict[str, Any]:
        """Analyze phone number using local phonenumbers library"""
        try:
            if not PHONENUMBERS_AVAILABLE:
                return {'error': 'phonenumbers library not available'}

            # Parse phone number
            parsed = phonenumbers.parse(phone, None)

            if not phonenumbers.is_valid_number(parsed):
                return {'error': 'Invalid phone number'}

            # Get information
            country = geocoder.description_for_number(parsed, 'en')
            carrier_name = carrier.name_for_number(parsed, 'en')

            return {
                'phone': phone,
                'valid': True,
                'country': country,
                'carrier': carrier_name,
                'number_type': phonenumbers.number_type(parsed),
                'international_format': phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.INTERNATIONAL),
                'national_format': phonenumbers.format_number(parsed, phonenumbers.PhoneNumberFormat.NATIONAL),
                'source': 'local_analysis'
            }

        except Exception as e:
            self.logger.error(f"Local phone analysis error: {e}")
            return {'error': str(e)}

    def validate_email_local(self, email: str) -> Dict[str, Any]:
        """Validate email using local validation"""
        try:
            if not EMAIL_VALIDATOR_AVAILABLE:
                return {'error': 'email-validator library not available'}

            validation = validate_email(email)

            return {
                'email': email,
                'valid': True,
                'normalized': validation.email,
                'local': validation.local,
                'domain': validation.domain,
                'ascii_email': validation.ascii_email,
                'ascii_local': validation.ascii_local,
                'ascii_domain': validation.ascii_domain,
                'source': 'local_validation'
            }

        except EmailNotValidError as e:
            return {
                'email': email,
                'valid': False,
                'error': str(e),
                'source': 'local_validation'
            }
        except Exception as e:
            self.logger.error(f"Email validation error: {e}")
            return {'error': str(e)}

    async def check_username_availability(self, username: str, platforms: List[str] = None) -> Dict[str, Any]:
        """Check username availability across platforms"""
        if platforms is None:
            platforms = ['facebook', 'instagram', 'twitter', 'linkedin', 'github', 'youtube']

        platform_urls = {
            'facebook': f'https://www.facebook.com/{username}',
            'instagram': f'https://www.instagram.com/{username}/',
            'twitter': f'https://twitter.com/{username}',
            'linkedin': f'https://www.linkedin.com/in/{username}',
            'github': f'https://github.com/{username}',
            'youtube': f'https://www.youtube.com/@{username}',
            'tiktok': f'https://www.tiktok.com/@{username}',
            'reddit': f'https://www.reddit.com/user/{username}'
        }

        results = {}

        await self.setup_session()

        for platform in platforms:
            if platform not in platform_urls:
                continue

            try:
                url = platform_urls[platform]
                async with self.session.get(url, allow_redirects=True) as response:
                    results[platform] = {
                        'url': url,
                        'exists': response.status == 200,
                        'status_code': response.status,
                        'available': response.status == 404
                    }

                # Small delay between requests
                await asyncio.sleep(0.5)

            except Exception as e:
                results[platform] = {
                    'url': platform_urls[platform],
                    'error': str(e),
                    'exists': None,
                    'available': None
                }

        return {
            'username': username,
            'platforms_checked': len(platforms),
            'platforms_found': sum(1 for r in results.values() if r.get('exists')),
            'results': results,
            'source': 'username_check'
        }

    async def comprehensive_email_osint(self, email: str) -> Dict[str, Any]:
        """Comprehensive OSINT for email address"""
        results = {
            'email': email,
            'timestamp': datetime.now().isoformat(),
            'sources': []
        }

        # Local validation
        validation = self.validate_email_local(email)
        if not validation.get('error'):
            results['validation'] = validation
            results['sources'].append('local_validation')

        # Domain extraction for Hunter.io
        domain = email.split('@')[1] if '@' in email else None
        if domain:
            hunter_data = await self.hunter_io_email_search(domain)
            if not hunter_data.get('error'):
                results['domain_emails'] = hunter_data
                results['sources'].append('hunter_io')

        # Breach check
        breach_data = await self.haveibeenpwned_check(email)
        if not breach_data.get('error'):
            results['breaches'] = breach_data
            results['sources'].append('haveibeenpwned')

        # Reputation check
        reputation_data = await self.emailrep_check(email)
        if not reputation_data.get('error'):
            results['reputation'] = reputation_data
            results['sources'].append('emailrep')

        return results

    async def comprehensive_phone_osint(self, phone: str) -> Dict[str, Any]:
        """Comprehensive OSINT for phone number"""
        results = {
            'phone': phone,
            'timestamp': datetime.now().isoformat(),
            'sources': []
        }

        # Local analysis
        local_analysis = self.analyze_phone_local(phone)
        if not local_analysis.get('error'):
            results['local_analysis'] = local_analysis
            results['sources'].append('local_analysis')

        # API verification
        api_verification = await self.numverify_phone_check(phone)
        if not api_verification.get('error'):
            results['api_verification'] = api_verification
            results['sources'].append('numverify')

        return results

    def generate_osint_report(self, email_data: Dict = None, phone_data: Dict = None,
                             username_data: Dict = None) -> Dict[str, Any]:
        """Generate comprehensive OSINT report"""
        report = {
            'report_id': hashlib.md5(f"{datetime.now().isoformat()}".encode()).hexdigest(),
            'generated_at': datetime.now().isoformat(),
            'summary': {},
            'risk_assessment': {},
            'recommendations': []
        }

        # Email analysis summary
        if email_data:
            report['email_analysis'] = email_data
            breach_count = email_data.get('breaches', {}).get('total_breaches', 0)
            report['summary']['email_breaches'] = breach_count

            if breach_count > 3:
                report['risk_assessment']['email_risk'] = 'high'
                report['recommendations'].append('Email found in multiple data breaches - consider changing passwords')
            elif breach_count > 0:
                report['risk_assessment']['email_risk'] = 'medium'
                report['recommendations'].append('Email found in data breaches - monitor for suspicious activity')
            else:
                report['risk_assessment']['email_risk'] = 'low'

        # Phone analysis summary
        if phone_data:
            report['phone_analysis'] = phone_data
            valid_phone = phone_data.get('local_analysis', {}).get('valid', False)
            report['summary']['phone_valid'] = valid_phone

            if valid_phone:
                report['risk_assessment']['phone_risk'] = 'low'
            else:
                report['risk_assessment']['phone_risk'] = 'medium'
                report['recommendations'].append('Phone number validation failed - verify authenticity')

        # Username analysis summary
        if username_data:
            report['username_analysis'] = username_data
            platforms_found = username_data.get('platforms_found', 0)
            report['summary']['cross_platform_presence'] = platforms_found

            if platforms_found > 5:
                report['risk_assessment']['exposure_risk'] = 'high'
                report['recommendations'].append('High cross-platform presence - consider privacy settings review')
            elif platforms_found > 2:
                report['risk_assessment']['exposure_risk'] = 'medium'
            else:
                report['risk_assessment']['exposure_risk'] = 'low'

        # Overall risk calculation
        risks = [v for k, v in report['risk_assessment'].items() if k.endswith('_risk')]
        high_risks = risks.count('high')
        medium_risks = risks.count('medium')

        if high_risks > 0:
            report['overall_risk'] = 'high'
        elif medium_risks > 1:
            report['overall_risk'] = 'medium'
        else:
            report['overall_risk'] = 'low'

        return report

    async def __aenter__(self):
        await self.setup_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close_session()

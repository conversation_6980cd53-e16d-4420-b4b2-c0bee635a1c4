#!/usr/bin/env python3
"""
أمثلة عملية لاستخدام نظام الاستخبارات الاجتماعية
Examples for Social Media Intelligence System
"""

import asyncio
import sys
import os
from datetime import datetime

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from social_accounts_standalone import EnhancedSocialMediaIntelligence
    from osint_collector import RealOSINTCollector
    from ai_analyzer import AIContentAnalyzer
    from web_scraper import RealWebScraper
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"❌ الوحدات غير متوفرة: {e}")
    MODULES_AVAILABLE = False

def print_separator(title):
    """طباعة فاصل جميل"""
    print("\n" + "="*60)
    print(f"🔹 {title}")
    print("="*60)

def print_result(key, value):
    """طباعة النتيجة بتنسيق جميل"""
    print(f"📊 {key}: {value}")

async def example_1_profile_analysis():
    """مثال 1: تحليل ملف شخصي شامل"""
    print_separator("مثال 1: تحليل ملف شخصي على Instagram")
    
    if not MODULES_AVAILABLE:
        print("❌ الوحدات المطلوبة غير متوفرة")
        return
    
    try:
        # إنشاء النظام
        intelligence = EnhancedSocialMediaIntelligence()
        intelligence.start_social_media_system()
        
        print("🔄 جاري تحليل الملف الشخصي...")
        
        # تحليل ملف شخصي (محاكاة آمنة)
        result = await intelligence.analyze_profile(
            platform="instagram",
            username_or_url="example_user",
            use_real_scraping=False,  # محاكاة للأمان
            include_osint=True,
            include_ai_analysis=True
        )
        
        if result and 'error' not in result:
            print("✅ تم التحليل بنجاح!")
            
            # النتائج الأساسية
            print_result("معرف التحليل", result.get('profile_id', 'غير متوفر'))
            print_result("المنصة", result.get('platform', 'غير متوفر'))
            print_result("اسم المستخدم", result.get('username', 'غير متوفر'))
            print_result("درجة المخاطر", f"{result.get('risk_score', 0.0):.2f}")
            print_result("الطرق المستخدمة", ', '.join(result.get('methods_used', [])))
            
            # بيانات الملف الشخصي
            profile_data = result.get('profile_data', {})
            if profile_data:
                print("\n📱 بيانات الملف الشخصي:")
                print_result("المتابعون", profile_data.get('follower_count', 'غير متوفر'))
                print_result("محقق", profile_data.get('verified', 'غير متوفر'))
                print_result("النبذة", profile_data.get('bio', 'غير متوفر')[:50] + "..." if profile_data.get('bio') else 'غير متوفر')
            
            # تحليل الذكاء الاصطناعي
            ai_analysis = result.get('ai_analysis', {})
            if ai_analysis:
                print("\n🤖 تحليل الذكاء الاصطناعي:")
                authenticity = ai_analysis.get('authenticity_assessment', {})
                if authenticity:
                    print_result("درجة الأصالة الإجمالية", f"{authenticity.get('overall_authenticity', 0.0):.2f}")
                    print_result("أصالة المحتوى", f"{authenticity.get('content_authenticity', 0.0):.2f}")
                    print_result("الاتساق السلوكي", f"{authenticity.get('behavioral_consistency', 0.0):.2f}")
            
            # بيانات OSINT
            osint_data = result.get('osint_data', {})
            if osint_data and 'comprehensive_report' in osint_data:
                print("\n🕵️ تقرير OSINT:")
                report = osint_data['comprehensive_report']
                print_result("المخاطر الإجمالية", report.get('overall_risk', 'غير معروف'))
                
                recommendations = report.get('recommendations', [])
                if recommendations:
                    print("\n💡 التوصيات:")
                    for i, rec in enumerate(recommendations[:3], 1):
                        print(f"   {i}. {rec}")
        else:
            print(f"❌ فشل التحليل: {result.get('error', 'خطأ غير معروف')}")
        
        # إغلاق النظام
        intelligence.stop_social_media_system()
        
    except Exception as e:
        print(f"❌ خطأ في المثال: {e}")

async def example_2_osint_investigation():
    """مثال 2: تحقيق OSINT لإيميل"""
    print_separator("مثال 2: تحقيق OSINT شامل لإيميل")
    
    if not MODULES_AVAILABLE:
        print("❌ الوحدات المطلوبة غير متوفرة")
        return
    
    try:
        async with RealOSINTCollector() as osint:
            print("🔄 جاري التحقيق في الإيميل...")
            
            # تحقيق شامل (سيستخدم APIs إذا كانت متوفرة)
            email = "<EMAIL>"
            result = await osint.comprehensive_email_osint(email)
            
            if result and 'error' not in result:
                print("✅ تم التحقيق بنجاح!")
                
                print_result("الإيميل المستهدف", result.get('email', 'غير متوفر'))
                print_result("وقت التحقيق", result.get('timestamp', 'غير متوفر'))
                print_result("المصادر المستخدمة", ', '.join(result.get('sources', [])))
                
                # التحقق من صحة الإيميل
                validation = result.get('validation', {})
                if validation:
                    print("\n📧 التحقق من صحة الإيميل:")
                    print_result("صحيح", validation.get('valid', 'غير معروف'))
                    print_result("النطاق", validation.get('domain', 'غير متوفر'))
                
                # فحص التسريبات
                breaches = result.get('breaches', {})
                if breaches:
                    print("\n🚨 فحص التسريبات:")
                    print_result("عدد التسريبات", breaches.get('total_breaches', 0))
                    print_result("مستوى المخاطر", breaches.get('risk_level', 'غير معروف'))
                
                # سمعة الإيميل
                reputation = result.get('reputation', {})
                if reputation:
                    print("\n🔍 سمعة الإيميل:")
                    print_result("مشبوه", reputation.get('suspicious', 'غير معروف'))
                    print_result("السمعة", reputation.get('reputation', 'غير معروف'))
            else:
                print(f"❌ فشل التحقيق: {result.get('error', 'خطأ غير معروف')}")
                
    except Exception as e:
        print(f"❌ خطأ في المثال: {e}")

def example_3_ai_content_analysis():
    """مثال 3: تحليل المحتوى بالذكاء الاصطناعي"""
    print_separator("مثال 3: تحليل المحتوى بالذكاء الاصطناعي")
    
    if not MODULES_AVAILABLE:
        print("❌ الوحدات المطلوبة غير متوفرة")
        return
    
    try:
        analyzer = AIContentAnalyzer()
        
        # نصوص للتحليل
        texts = [
            "أحب هذا المنتج الرائع! إنه يعمل بشكل مثالي ومذهل.",
            "هذا المنتج سيء جداً ولا أنصح به أبداً.",
            "المنتج عادي، لا سيء ولا جيد."
        ]
        
        for i, text in enumerate(texts, 1):
            print(f"\n📝 تحليل النص {i}: '{text}'")
            
            # تحليل المشاعر باستخدام TextBlob
            sentiment_result = analyzer.analyze_sentiment_textblob(text)
            
            if sentiment_result and 'error' not in sentiment_result:
                print_result("المشاعر", sentiment_result.get('sentiment', 'غير معروف'))
                print_result("القطبية", f"{sentiment_result.get('polarity', 0.0):.2f}")
                print_result("الذاتية", f"{sentiment_result.get('subjectivity', 0.0):.2f}")
                print_result("الثقة", f"{sentiment_result.get('confidence', 0.0):.2f}")
            
            # إحصائيات النص
            stats = analyzer.extract_text_statistics(text)
            if stats:
                print_result("عدد الكلمات", stats.get('word_count', 0))
                print_result("عدد الأحرف", stats.get('character_count', 0))
                print_result("علامات التعجب", stats.get('exclamation_count', 0))
                print_result("نسبة الأحرف الكبيرة", f"{stats.get('uppercase_ratio', 0.0):.2f}")
            
            # تحليل أنماط اللغة
            patterns = analyzer.analyze_language_patterns(text)
            if patterns:
                print_result("أسلوب الكتابة", patterns.get('writing_style', 'غير معروف'))
                print_result("مستوى الرسمية", f"{patterns.get('formality_level', 0.0):.2f}")
                print_result("شدة المشاعر", f"{patterns.get('emotional_intensity', 0.0):.2f}")
        
    except Exception as e:
        print(f"❌ خطأ في المثال: {e}")

async def example_4_educational_account_creation():
    """مثال 4: إنشاء حساب تعليمي"""
    print_separator("مثال 4: إنشاء حساب تعليمي (محاكاة آمنة)")
    
    if not MODULES_AVAILABLE:
        print("❌ الوحدات المطلوبة غير متوفرة")
        return
    
    try:
        intelligence = EnhancedSocialMediaIntelligence()
        intelligence.start_social_media_system()
        
        print("🎓 إنشاء حساب تعليمي (وضع المحاكاة الآمن)...")
        
        # إنشاء حساب تعليمي
        result = await intelligence.create_fake_account(
            platform="instagram",
            account_type="educational",
            educational_mode=True  # وضع آمن - محاكاة فقط
        )
        
        if result and 'error' not in result:
            print("✅ تم إنشاء الحساب التعليمي بنجاح!")
            
            print_result("المنصة", result.get('platform', 'غير متوفر'))
            print_result("اسم المستخدم", result.get('username', 'غير متوفر'))
            print_result("الإيميل", result.get('email', 'غير متوفر'))
            print_result("الحالة", result.get('status', 'غير متوفر'))
            print_result("الوضع التعليمي", result.get('educational_mode', False))
            print_result("تاريخ الإنشاء", result.get('created_at', 'غير متوفر'))
            
            # معلومات الهوية المولدة
            identity = result.get('identity', {})
            if identity:
                print("\n👤 الهوية المولدة:")
                print_result("الاسم الكامل", identity.get('full_name', 'غير متوفر'))
                print_result("الجنس", identity.get('gender', 'غير متوفر'))
                print_result("الموقع", identity.get('location', 'غير متوفر'))
            
            print("\n📝 ملاحظة مهمة:")
            print("   هذا حساب محاكاة لأغراض تعليمية فقط")
            print("   لم يتم إنشاء حساب حقيقي على أي منصة")
            print("   جميع البيانات مولدة عشوائياً للتعلم")
        else:
            print(f"❌ فشل إنشاء الحساب: {result.get('error', 'خطأ غير معروف')}")
        
        intelligence.stop_social_media_system()
        
    except Exception as e:
        print(f"❌ خطأ في المثال: {e}")

def example_5_system_status():
    """مثال 5: عرض حالة النظام والقدرات"""
    print_separator("مثال 5: حالة النظام والقدرات")
    
    if not MODULES_AVAILABLE:
        print("❌ الوحدات المطلوبة غير متوفرة")
        return
    
    try:
        intelligence = EnhancedSocialMediaIntelligence()
        
        print("🔧 قدرات النظام:")
        for capability, enabled in intelligence.capabilities.items():
            status = "✅" if enabled else "❌"
            print(f"   {status} {capability.replace('_', ' ').title()}")
        
        print("\n🌐 المنصات المدعومة:")
        for platform, config in intelligence.platforms.items():
            enabled = "✅" if config['enabled'] else "❌"
            real_scraping = "✅" if config.get('real_scraping', False) else "❌"
            print(f"   {enabled} {platform.title()}")
            print(f"      - Real Scraping: {real_scraping}")
            print(f"      - Analysis Features: {len(config.get('analysis_features', []))}")
            print(f"      - OSINT Features: {len(config.get('osint_features', []))}")
        
        print("\n📊 إحصائيات النظام:")
        for metric, count in intelligence.stats.items():
            print_result(metric.replace('_', ' ').title(), count)
        
        # حالة الأمان
        if intelligence.security_manager:
            security_status = intelligence.security_manager.get_security_status()
            print("\n🔒 حالة الأمان:")
            print_result("التشفير مفعل", "✅" if security_status['encryption_enabled'] else "❌")
            print_result("سجل المراجعة مفعل", "✅" if security_status['audit_logging_enabled'] else "❌")
            print_result("الموافقة القانونية", "✅" if security_status['legal_consent'] else "❌")
            print_result("الوضع التعليمي", "✅" if security_status['educational_mode'] else "❌")
        
    except Exception as e:
        print(f"❌ خطأ في المثال: {e}")

async def run_all_examples():
    """تشغيل جميع الأمثلة"""
    print("🚀 تشغيل جميع الأمثلة العملية")
    print("=" * 60)
    
    # عرض حالة النظام أولاً
    example_5_system_status()
    
    # تشغيل الأمثلة
    await example_1_profile_analysis()
    await example_2_osint_investigation()
    example_3_ai_content_analysis()
    await example_4_educational_account_creation()
    
    print_separator("انتهت جميع الأمثلة")
    print("🎉 تم تشغيل جميع الأمثلة بنجاح!")
    print("\n💡 نصائح:")
    print("   - استخدم الوضع التعليمي دائماً للاختبار")
    print("   - أضف مفاتيح API للحصول على نتائج حقيقية")
    print("   - اقرأ التحذيرات القانونية قبل الاستخدام")

def interactive_menu():
    """قائمة تفاعلية لاختيار الأمثلة"""
    while True:
        print("\n🎯 اختر مثالاً لتشغيله:")
        print("1. تحليل ملف شخصي شامل")
        print("2. تحقيق OSINT لإيميل")
        print("3. تحليل المحتوى بالذكاء الاصطناعي")
        print("4. إنشاء حساب تعليمي")
        print("5. عرض حالة النظام")
        print("6. تشغيل جميع الأمثلة")
        print("0. خروج")
        
        choice = input("\nاختر رقم المثال (0-6): ").strip()
        
        try:
            if choice == "0":
                print("👋 وداعاً!")
                break
            elif choice == "1":
                asyncio.run(example_1_profile_analysis())
            elif choice == "2":
                asyncio.run(example_2_osint_investigation())
            elif choice == "3":
                example_3_ai_content_analysis()
            elif choice == "4":
                asyncio.run(example_4_educational_account_creation())
            elif choice == "5":
                example_5_system_status()
            elif choice == "6":
                asyncio.run(run_all_examples())
            else:
                print("❌ اختيار غير صحيح")
            
            input("\nاضغط Enter للمتابعة...")
            
        except KeyboardInterrupt:
            print("\n👋 تم إيقاف التشغيل")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
            input("اضغط Enter للمتابعة...")

if __name__ == "__main__":
    print("📚 أمثلة عملية لنظام الاستخبارات الاجتماعية")
    print("=" * 60)
    
    if not MODULES_AVAILABLE:
        print("❌ لا يمكن تشغيل الأمثلة - الوحدات المطلوبة غير متوفرة")
        print("💡 تأكد من:")
        print("   1. تفعيل البيئة الافتراضية: source venv/bin/activate")
        print("   2. تثبيت المتطلبات: pip install -r requirements.txt")
        sys.exit(1)
    
    # تشغيل القائمة التفاعلية
    interactive_menu()

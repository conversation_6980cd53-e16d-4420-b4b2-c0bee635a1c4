# Enhanced Social Media Intelligence System - Environment Configuration
# Copy this file to .env and fill in your API keys

# =============================================================================
# OSINT API KEYS
# =============================================================================

# Hunter.io - Email discovery and verification
# Get your key from: https://hunter.io/api
HUNTER_IO_API_KEY=your_hunter_io_api_key_here

# HaveIBeenPwned - Data breach checking
# Get your key from: https://haveibeenpwned.com/API/Key
HIBP_API_KEY=your_haveibeenpwned_api_key_here

# EmailRep.io - Email reputation checking
# Get your key from: https://emailrep.io/
EMAILREP_API_KEY=your_emailrep_api_key_here

# Numverify - Phone number validation
# Get your key from: https://numverify.com/
NUMVERIFY_API_KEY=your_numverify_api_key_here

# =============================================================================
# AUTOMATION SERVICES
# =============================================================================

# 2captcha - CAPTCHA solving service
# Get your key from: https://2captcha.com/
TWOCAPTCHA_API_KEY=your_2captcha_api_key_here

# Temporary email services
TEMPMAIL_API_KEY=your_tempmail_api_key_here

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Enable/disable encryption for sensitive data
ENCRYPTION_ENABLED=true

# Educational mode (safer, simulated operations)
EDUCATIONAL_MODE=true

# Enable comprehensive audit logging
AUDIT_LOGGING=true

# Data retention period (days)
DATA_RETENTION_DAYS=30

# Maximum concurrent requests
MAX_CONCURRENT_REQUESTS=5

# =============================================================================
# SELENIUM CONFIGURATION
# =============================================================================

# Run browser in headless mode
SELENIUM_HEADLESS=true

# Browser window size
SELENIUM_WINDOW_WIDTH=1920
SELENIUM_WINDOW_HEIGHT=1080

# Page load timeout (seconds)
SELENIUM_PAGE_TIMEOUT=30

# Proxy settings (optional)
# SELENIUM_PROXY=ip:port

# =============================================================================
# AI/ML SETTINGS
# =============================================================================

# Device for AI models (cpu/cuda)
AI_DEVICE=cpu

# Maximum text length for analysis
AI_MAX_LENGTH=512

# Batch size for processing
AI_BATCH_SIZE=16

# =============================================================================
# DATABASE SETTINGS
# =============================================================================

# Database file path
DATABASE_PATH=data/social_intelligence.db

# Backup directory
BACKUP_PATH=data/backups

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log file path
LOG_FILE=logs/social_intelligence.log

# Maximum log file size (MB)
LOG_MAX_SIZE=10

# Number of backup log files
LOG_BACKUP_COUNT=5

# =============================================================================
# RATE LIMITING
# =============================================================================

# Rate limits for different platforms (seconds between requests)
FACEBOOK_RATE_LIMIT=2
INSTAGRAM_RATE_LIMIT=3
TWITTER_RATE_LIMIT=1
LINKEDIN_RATE_LIMIT=5

# =============================================================================
# LEGAL AND COMPLIANCE
# =============================================================================

# Require user consent before operations
REQUIRE_CONSENT=true

# Terms and conditions version
TERMS_VERSION=1.0

# Enable anonymization of collected data
ANONYMIZE_DATA=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable debug mode
DEBUG_MODE=false

# Enable verbose logging
VERBOSE_LOGGING=false

# Enable performance monitoring
PERFORMANCE_MONITORING=true

# Cache settings
ENABLE_CACHE=true
CACHE_TTL=3600
MAX_CACHE_SIZE=1000

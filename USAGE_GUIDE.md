# 📖 دليل الاستخدام الشامل - نظام الاستخبارات الاجتماعية

## 🚀 البدء السريع

### 1. تفعيل البيئة الافتراضية
```bash
# تفعيل البيئة
source venv/bin/activate

# التأكد من التفعيل (يجب أن ترى (venv) في بداية السطر)
which python
```

### 2. إعداد ملف البيئة
```bash
# نسخ ملف الإعدادات
cp .env.example .env

# تحرير الملف وإضافة مفاتيح API الخاصة بك
nano .env
```

### 3. اختبار النظام
```bash
# اختبار سريع للتأكد من عمل النظام
python -c "from social_accounts_standalone import EnhancedSocialMediaIntelligence; print('✅ النظام يعمل بنجاح!')"
```

## 🖥️ طرق التشغيل المختلفة

### 1. القائمة السريعة (الأسهل)
```bash
python run.py
```
**الميزات:**
- قائمة تفاعلية سهلة
- خيارات متنوعة
- مناسبة للمبتدئين

### 2. واجهة CLI التفاعلية (الأكثر تفصيلاً)
```bash
python cli_interface.py
```
**الميزات:**
- واجهة جميلة مع ألوان
- تقارير مفصلة
- تحكم كامل في الخيارات

### 3. النظام الرئيسي (للمطورين)
```bash
python social_accounts_standalone.py
```
**الميزات:**
- تشغيل مباشر للنظام
- عرض تجريبي شامل
- مناسب للاختبار والتطوير

## 📋 أمثلة عملية مفصلة

### 🔍 مثال 1: تحليل ملف شخصي على Instagram

#### الطريقة الأولى: عبر القائمة السريعة
```bash
python run.py
# اختر: 2 - Quick Profile Analysis
# Platform: instagram
# Username: example_user
```

#### الطريقة الثانية: عبر CLI
```bash
python cli_interface.py
# اختر: 1 - Analyze Profile (Enhanced)
# Platform: instagram
# Username: example_user
# Use real scraping: Yes
# Include OSINT: Yes
# Include AI analysis: Yes
```

#### الطريقة الثالثة: برمجياً
```python
import asyncio
from social_accounts_standalone import EnhancedSocialMediaIntelligence

async def analyze_instagram_profile():
    # إنشاء النظام
    intelligence = EnhancedSocialMediaIntelligence()
    intelligence.start_social_media_system()
    
    # تحليل الملف الشخصي
    result = await intelligence.analyze_profile(
        platform="instagram",
        username_or_url="example_user",
        use_real_scraping=True,
        include_osint=True,
        include_ai_analysis=True
    )
    
    # عرض النتائج
    if result and 'error' not in result:
        print(f"📊 درجة المخاطر: {result.get('risk_score', 0.0):.2f}")
        print(f"👥 المتابعون: {result.get('profile_data', {}).get('follower_count', 'غير متوفر')}")
        print(f"✅ محقق: {result.get('profile_data', {}).get('verified', False)}")
        
        # تحليل AI
        ai_analysis = result.get('ai_analysis', {})
        if ai_analysis:
            authenticity = ai_analysis.get('authenticity_assessment', {})
            print(f"🤖 درجة الأصالة: {authenticity.get('overall_authenticity', 0.0):.2f}")
    else:
        print(f"❌ فشل التحليل: {result.get('error', 'خطأ غير معروف')}")
    
    # إغلاق النظام
    intelligence.stop_social_media_system()

# تشغيل المثال
asyncio.run(analyze_instagram_profile())
```

### 🕵️ مثال 2: تحقيق OSINT لإيميل

#### عبر القائمة السريعة:
```bash
python run.py
# اختر: 3 - OSINT Investigation
# Type: email
# Enter email: <EMAIL>
```

#### برمجياً:
```python
import asyncio
from osint_collector import RealOSINTCollector

async def investigate_email():
    async with RealOSINTCollector() as osint:
        # تحقيق شامل للإيميل
        result = await osint.comprehensive_email_osint("<EMAIL>")
        
        if result and 'error' not in result:
            print("📧 نتائج تحقيق الإيميل:")
            
            # فحص التسريبات
            breaches = result.get('breaches', {})
            if breaches:
                print(f"🚨 التسريبات: {breaches.get('total_breaches', 0)}")
                print(f"⚠️ مستوى المخاطر: {breaches.get('risk_level', 'غير معروف')}")
            
            # سمعة الإيميل
            reputation = result.get('reputation', {})
            if reputation:
                print(f"🔍 مشبوه: {reputation.get('suspicious', 'غير معروف')}")
            
            # المصادر المستخدمة
            sources = result.get('sources', [])
            print(f"📊 المصادر: {', '.join(sources)}")

asyncio.run(investigate_email())
```

### 🤖 مثال 3: تحليل المحتوى بالذكاء الاصطناعي

#### عبر القائمة السريعة:
```bash
python run.py
# اختر: 4 - AI Content Analysis Demo
# Enter text: "I absolutely love this amazing product! It works perfectly."
```

#### برمجياً:
```python
from ai_analyzer import AIContentAnalyzer

def analyze_content():
    analyzer = AIContentAnalyzer()
    
    text = "أحب هذا المنتج الرائع! إنه يعمل بشكل مثالي ومذهل."
    
    # تحليل شامل للنص
    analysis = analyzer.analyze_text_comprehensive(text)
    
    print("🤖 نتائج تحليل المحتوى:")
    
    # تحليل المشاعر
    sentiment = analysis.get('sentiment', {})
    textblob_result = sentiment.get('textblob', {})
    if textblob_result:
        print(f"😊 المشاعر: {textblob_result.get('sentiment', 'غير معروف')}")
        print(f"📊 القطبية: {textblob_result.get('polarity', 0.0):.2f}")
        print(f"🎭 الذاتية: {textblob_result.get('subjectivity', 0.0):.2f}")
    
    # إحصائيات النص
    stats = analysis.get('statistics', {})
    if stats:
        print(f"📝 عدد الكلمات: {stats.get('word_count', 0)}")
        print(f"📏 عدد الأحرف: {stats.get('character_count', 0)}")
        print(f"❗ علامات التعجب: {stats.get('exclamation_count', 0)}")

analyze_content()
```

### 🎓 مثال 4: إنشاء حساب تعليمي

#### عبر القائمة السريعة:
```bash
python run.py
# اختر: 5 - Educational Account Creation
# Platform: instagram
# (اقرأ التحذيرات واوافق)
```

#### برمجياً:
```python
import asyncio
from social_accounts_standalone import EnhancedSocialMediaIntelligence

async def create_educational_account():
    intelligence = EnhancedSocialMediaIntelligence()
    intelligence.start_social_media_system()
    
    # إنشاء حساب تعليمي (محاكاة آمنة)
    result = await intelligence.create_fake_account(
        platform="instagram",
        account_type="educational",
        educational_mode=True  # وضع آمن
    )
    
    if result and 'error' not in result:
        print("🎓 تم إنشاء حساب تعليمي:")
        print(f"👤 اسم المستخدم: {result.get('username', 'غير متوفر')}")
        print(f"📧 الإيميل: {result.get('email', 'غير متوفر')}")
        print(f"📊 الحالة: {result.get('status', 'غير متوفر')}")
        print(f"🎓 وضع تعليمي: {result.get('educational_mode', False)}")
        print("\n📝 ملاحظة: هذا حساب محاكاة لأغراض تعليمية فقط")
    else:
        print(f"❌ فشل إنشاء الحساب: {result.get('error', 'خطأ غير معروف')}")
    
    intelligence.stop_social_media_system()

asyncio.run(create_educational_account())
```

## 🔧 إعداد مفاتيح API

### 1. Hunter.io (البحث عن الإيميلات)
```bash
# في ملف .env
HUNTER_IO_API_KEY=your_hunter_io_key_here
```
**كيفية الحصول عليه:**
1. اذهب إلى https://hunter.io/api
2. أنشئ حساب مجاني
3. احصل على مفتاح API من لوحة التحكم

### 2. HaveIBeenPwned (فحص التسريبات)
```bash
# في ملف .env
HIBP_API_KEY=your_haveibeenpwned_key_here
```
**كيفية الحصول عليه:**
1. اذهب إلى https://haveibeenpwned.com/API/Key
2. ادفع الرسوم المطلوبة (حوالي $3.50/شهر)
3. احصل على مفتاح API

### 3. 2captcha (حل الكابتشا)
```bash
# في ملف .env
TWOCAPTCHA_API_KEY=your_2captcha_key_here
```
**كيفية الحصول عليه:**
1. اذهب إلى https://2captcha.com/
2. أنشئ حساب وأضف رصيد
3. احصل على مفتاح API من الإعدادات

## 📊 فهم النتائج

### درجات المخاطر
- **0.0 - 0.3**: مخاطر منخفضة (أخضر)
- **0.3 - 0.7**: مخاطر متوسطة (أصفر)
- **0.7 - 1.0**: مخاطر عالية (أحمر)

### درجات الأصالة (AI)
- **0.8 - 1.0**: أصيل جداً
- **0.6 - 0.8**: أصيل إلى حد كبير
- **0.4 - 0.6**: مشكوك فيه
- **0.0 - 0.4**: مشبوه جداً

### تحليل المشاعر
- **Positive**: إيجابي (0.1 إلى 1.0)
- **Neutral**: محايد (-0.1 إلى 0.1)
- **Negative**: سلبي (-1.0 إلى -0.1)

## 🛠️ استكشاف الأخطاء

### مشكلة: "Module not found"
```bash
# تأكد من تفعيل البيئة الافتراضية
source venv/bin/activate

# أعد تثبيت المكتبات
pip install -r requirements.txt
```

### مشكلة: "ChromeDriver not found"
```bash
# تثبيت Chrome/Chromium
sudo apt-get install chromium-browser

# أو تحديث webdriver-manager
pip install --upgrade webdriver-manager
```

### مشكلة: "API key not configured"
```bash
# تأكد من وجود ملف .env
ls -la .env

# تأكد من صحة مفاتيح API
cat .env | grep API_KEY
```

## 🔒 نصائح الأمان

### 1. استخدم الوضع التعليمي
```python
# دائماً استخدم educational_mode=True للاختبار
educational_mode=True
```

### 2. احترم معدلات الطلبات
```python
# لا تفرط في الطلبات
import time
time.sleep(2)  # انتظر بين الطلبات
```

### 3. احم مفاتيح API
```bash
# لا تشارك ملف .env
echo ".env" >> .gitignore
```

## 📞 الحصول على المساعدة

### 1. تشغيل الاختبارات
```bash
python -m pytest test_enhanced_system.py -v
```

### 2. عرض حالة النظام
```bash
python run.py
# اختر: 6 - System Status & Capabilities
```

### 3. عرض المساعدة
```bash
python run.py
# اختر: 9 - Help & Documentation
```

## ⚖️ تذكير قانوني

**استخدم هذا النظام بمسؤولية:**
- ✅ للبحث والتعليم فقط
- ✅ مع الحصول على إذن مسبق
- ❌ لا تستخدمه لأغراض ضارة
- ❌ لا تنتهك شروط الخدمة

---

**🎯 نصيحة:** ابدأ دائماً بالوضع التعليمي والمحاكاة قبل استخدام الميزات الحقيقية!

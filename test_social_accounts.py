#!/usr/bin/env python3
# Social Media Accounts Test Suite

import sys
import time
import json
from datetime import datetime

# Import the standalone module
from social_accounts_standalone import StandaloneSocialMediaAccounts

class SocialMediaAccountsTester:
    def __init__(self):
        self.social_media = StandaloneSocialMediaAccounts()
        self.test_results = []
        
    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("📱 SOCIAL MEDIA ACCOUNTS TEST SUITE")
        print("=" * 60)
        
        # Test 1: System Initialization
        print("\n🔧 Test 1: System Initialization")
        self.test_system_initialization()
        
        # Test 2: Profile Analysis
        print("\n📊 Test 2: Profile Analysis")
        self.test_profile_analysis()
        
        # Test 3: OSINT Gathering
        print("\n🕵️ Test 3: OSINT Intelligence Gathering")
        self.test_osint_gathering()
        
        # Test 4: Advanced Analysis
        print("\n🧠 Test 4: Advanced Profile Analysis")
        self.test_advanced_analysis()
        
        # Test 5: Fake Account Creation
        print("\n🎭 Test 5: Fake Account Creation")
        self.test_fake_account_creation()
        
        # Test 6: Cross-Platform Analysis
        print("\n🌐 Test 6: Cross-Platform Analysis")
        self.test_cross_platform_analysis()
        
        # Test 7: Database Operations
        print("\n💾 Test 7: Database Operations")
        self.test_database_operations()
        
        # Test 8: System Performance
        print("\n⚡ Test 8: System Performance")
        self.test_system_performance()
        
        # Generate test report
        self.generate_test_report()
    
    def test_system_initialization(self):
        """Test system initialization"""
        try:
            # Start system
            success = self.social_media.start_social_media_system()
            
            if success:
                print("[✓] System started successfully")
                
                # Check capabilities
                status = self.social_media.get_system_status()
                capabilities = status['capabilities']
                platforms = status['platforms']
                
                print(f"[*] Capabilities enabled: {sum(capabilities.values())}/{len(capabilities)}")
                for capability, enabled in capabilities.items():
                    status_icon = "✓" if enabled else "✗"
                    print(f"    [{status_icon}] {capability}")
                
                print(f"[*] Platforms supported: {len(platforms)}")
                for platform, config in platforms.items():
                    print(f"    [✓] {platform}: {len(config['analysis_features'])} analysis features")
                
                self.test_results.append({
                    'test': 'system_initialization',
                    'status': 'passed',
                    'details': f"Capabilities: {sum(capabilities.values())}/{len(capabilities)}, Platforms: {len(platforms)}"
                })
            else:
                print("[✗] System failed to start")
                self.test_results.append({
                    'test': 'system_initialization',
                    'status': 'failed',
                    'details': 'System startup failed'
                })
                
        except Exception as e:
            print(f"[✗] Initialization test error: {e}")
            self.test_results.append({
                'test': 'system_initialization',
                'status': 'error',
                'details': str(e)
            })
    
    def test_profile_analysis(self):
        """Test profile analysis functionality"""
        try:
            test_profiles = [
                ('facebook', 'testuser123'),
                ('instagram', 'influencer456'),
                ('linkedin', 'professional789'),
                ('twitter', 'tweeter101'),
                ('tiktok', 'creator202'),
                ('youtube', 'channel303')
            ]
            
            successful_analyses = 0
            
            for platform, username in test_profiles:
                print(f"[*] Analyzing {platform} profile: {username}")
                
                analysis = self.social_media.analyze_profile(platform, username)
                
                if analysis:
                    successful_analyses += 1
                    risk_score = analysis.get('risk_score', 0.0)
                    
                    profile_data = analysis.get('profile_data', {})
                    follower_count = profile_data.get('follower_count', 0)
                    verified = profile_data.get('verified', False)
                    
                    print(f"    - Risk Score: {risk_score:.2f}")
                    print(f"    - Followers: {follower_count}")
                    print(f"    - Verified: {verified}")
                    
                    # Check required fields
                    required_fields = ['profile_id', 'platform', 'username', 'profile_data']
                    complete = all(field in analysis for field in required_fields)
                    
                    if complete:
                        print(f"    [✓] Analysis complete")
                    else:
                        print(f"    [!] Analysis incomplete")
                else:
                    print(f"    [✗] Analysis failed")
            
            success_rate = successful_analyses / len(test_profiles)
            print(f"[*] Analysis success rate: {success_rate:.2%}")
            
            if success_rate >= 0.8:  # 80% success rate threshold
                print("[✓] Profile analysis test passed")
                self.test_results.append({
                    'test': 'profile_analysis',
                    'status': 'passed',
                    'details': f"Success rate: {success_rate:.2%}"
                })
            else:
                print("[✗] Profile analysis test failed")
                self.test_results.append({
                    'test': 'profile_analysis',
                    'status': 'failed',
                    'details': f"Low success rate: {success_rate:.2%}"
                })
                
        except Exception as e:
            print(f"[✗] Profile analysis test error: {e}")
            self.test_results.append({
                'test': 'profile_analysis',
                'status': 'error',
                'details': str(e)
            })
    
    def test_osint_gathering(self):
        """Test OSINT gathering capabilities"""
        try:
            test_profile = ('facebook', 'testuser123')
            platform, username = test_profile
            
            print(f"[*] Gathering OSINT for: {platform}/{username}")
            
            # Analyze profile to trigger OSINT gathering
            analysis = self.social_media.analyze_profile(platform, username)
            
            if analysis and 'osint_data' in analysis:
                osint_data = analysis['osint_data']
                
                # Check OSINT components
                components = [
                    'cross_platform_presence', 'email_discovery', 'phone_discovery',
                    'data_breach_exposure', 'social_connections', 'content_analysis',
                    'behavioral_patterns'
                ]
                found_components = 0
                
                for component in components:
                    if component in osint_data and not osint_data[component].get('error'):
                        found_components += 1
                        print(f"    [✓] {component}")
                    else:
                        print(f"    [✗] {component}")
                
                # Check specific OSINT results
                cross_platform = osint_data.get('cross_platform_presence', {})
                platforms_found = sum(1 for platform_data in cross_platform.values() if platform_data.get('found', False))
                print(f"    [*] Cross-platform presence: {platforms_found} platforms")
                
                emails = osint_data.get('email_discovery', [])
                print(f"    [*] Email addresses found: {len(emails)}")
                
                breaches = osint_data.get('data_breach_exposure', {})
                breach_count = breaches.get('total_breaches', 0)
                print(f"    [*] Data breaches found: {breach_count}")
                
                if found_components >= 5:  # At least 5 components working
                    print("[✓] OSINT gathering test passed")
                    self.test_results.append({
                        'test': 'osint_gathering',
                        'status': 'passed',
                        'details': f"Components working: {found_components}/{len(components)}"
                    })
                else:
                    print("[✗] OSINT gathering test failed")
                    self.test_results.append({
                        'test': 'osint_gathering',
                        'status': 'failed',
                        'details': f"Insufficient components: {found_components}/{len(components)}"
                    })
            else:
                print("[✗] No OSINT data gathered")
                self.test_results.append({
                    'test': 'osint_gathering',
                    'status': 'failed',
                    'details': 'No OSINT data in analysis'
                })
                
        except Exception as e:
            print(f"[✗] OSINT gathering test error: {e}")
            self.test_results.append({
                'test': 'osint_gathering',
                'status': 'error',
                'details': str(e)
            })
    
    def test_advanced_analysis(self):
        """Test advanced analysis features"""
        try:
            test_profile = ('instagram', 'influencer456')
            platform, username = test_profile
            
            print(f"[*] Advanced analysis for: {platform}/{username}")
            
            analysis = self.social_media.analyze_profile(platform, username)
            
            if analysis and 'analysis_data' in analysis:
                analysis_data = analysis['analysis_data']
                
                # Check analysis components
                components = [
                    'authenticity_score', 'influence_score', 'vulnerability_assessment',
                    'attack_surface', 'social_engineering_vectors', 'impersonation_risk'
                ]
                found_components = 0
                
                for component in components:
                    if component in analysis_data:
                        found_components += 1
                        print(f"    [✓] {component}")
                        
                        # Show specific values
                        if component in ['authenticity_score', 'influence_score']:
                            score = analysis_data[component]
                            print(f"        Score: {score:.2f}")
                        elif component == 'vulnerability_assessment':
                            vulns = analysis_data[component]
                            print(f"        Vulnerabilities: {len(vulns)}")
                        elif component == 'impersonation_risk':
                            risk = analysis_data[component]
                            print(f"        Risk Level: {risk.get('risk_level', 'unknown')}")
                    else:
                        print(f"    [✗] {component}")
                
                if found_components >= 4:  # At least 4 components working
                    print("[✓] Advanced analysis test passed")
                    self.test_results.append({
                        'test': 'advanced_analysis',
                        'status': 'passed',
                        'details': f"Components working: {found_components}/{len(components)}"
                    })
                else:
                    print("[✗] Advanced analysis test failed")
                    self.test_results.append({
                        'test': 'advanced_analysis',
                        'status': 'failed',
                        'details': f"Insufficient components: {found_components}/{len(components)}"
                    })
            else:
                print("[✗] No advanced analysis data found")
                self.test_results.append({
                    'test': 'advanced_analysis',
                    'status': 'failed',
                    'details': 'No analysis data in results'
                })
                
        except Exception as e:
            print(f"[✗] Advanced analysis test error: {e}")
            self.test_results.append({
                'test': 'advanced_analysis',
                'status': 'error',
                'details': str(e)
            })
    
    def test_fake_account_creation(self):
        """Test fake account creation"""
        try:
            platforms = ['facebook', 'instagram', 'twitter', 'linkedin']
            successful_creations = 0
            
            for platform in platforms:
                print(f"[*] Creating fake {platform} account...")
                
                fake_account = self.social_media.create_fake_account(platform)
                
                if fake_account:
                    successful_creations += 1
                    username = fake_account.get('username', 'unknown')
                    email = fake_account.get('email', 'unknown')
                    
                    print(f"    - Username: {username}")
                    print(f"    - Email: {email}")
                    
                    # Check required fields
                    required_fields = ['account_id', 'platform', 'username', 'password', 'email']
                    complete = all(field in fake_account for field in required_fields)
                    
                    if complete:
                        print(f"    [✓] Account creation complete")
                    else:
                        print(f"    [!] Account creation incomplete")
                else:
                    print(f"    [✗] Account creation failed")
            
            success_rate = successful_creations / len(platforms)
            print(f"[*] Account creation success rate: {success_rate:.2%}")
            
            if success_rate >= 0.75:  # 75% success rate threshold
                print("[✓] Fake account creation test passed")
                self.test_results.append({
                    'test': 'fake_account_creation',
                    'status': 'passed',
                    'details': f"Success rate: {success_rate:.2%}"
                })
            else:
                print("[✗] Fake account creation test failed")
                self.test_results.append({
                    'test': 'fake_account_creation',
                    'status': 'failed',
                    'details': f"Low success rate: {success_rate:.2%}"
                })
                
        except Exception as e:
            print(f"[✗] Fake account creation test error: {e}")
            self.test_results.append({
                'test': 'fake_account_creation',
                'status': 'error',
                'details': str(e)
            })
    
    def test_cross_platform_analysis(self):
        """Test cross-platform analysis"""
        try:
            username = 'testuser123'
            platforms = ['facebook', 'instagram', 'twitter']
            
            print(f"[*] Cross-platform analysis for username: {username}")
            
            analyses = []
            for platform in platforms:
                analysis = self.social_media.analyze_profile(platform, username)
                if analysis:
                    analyses.append(analysis)
            
            if len(analyses) >= 2:
                # Check for cross-platform correlations
                print(f"[*] Analyzed {len(analyses)} platforms")
                
                # Check OSINT cross-platform data
                cross_platform_data = []
                for analysis in analyses:
                    osint_data = analysis.get('osint_data', {})
                    cross_platform = osint_data.get('cross_platform_presence', {})
                    cross_platform_data.append(cross_platform)
                
                if cross_platform_data:
                    print("[✓] Cross-platform analysis test passed")
                    self.test_results.append({
                        'test': 'cross_platform_analysis',
                        'status': 'passed',
                        'details': f"Analyzed {len(analyses)} platforms"
                    })
                else:
                    print("[!] Limited cross-platform data")
                    self.test_results.append({
                        'test': 'cross_platform_analysis',
                        'status': 'warning',
                        'details': 'Limited cross-platform correlation data'
                    })
            else:
                print("[✗] Insufficient platform analyses")
                self.test_results.append({
                    'test': 'cross_platform_analysis',
                    'status': 'failed',
                    'details': f"Only {len(analyses)} platforms analyzed"
                })
                
        except Exception as e:
            print(f"[✗] Cross-platform analysis test error: {e}")
            self.test_results.append({
                'test': 'cross_platform_analysis',
                'status': 'error',
                'details': str(e)
            })
    
    def test_database_operations(self):
        """Test database operations"""
        try:
            print("[*] Testing database operations...")
            
            # Test profile intelligence storage
            test_analysis = {
                'profile_id': 'test_profile_123',
                'platform': 'facebook',
                'username': 'testuser',
                'profile_data': {'test': 'data'},
                'osint_data': {'test': 'osint'},
                'analysis_data': {'test': 'analysis'},
                'risk_score': 0.75
            }
            
            self.social_media.store_profile_intelligence(test_analysis)
            print("[✓] Profile intelligence storage test passed")
            
            # Test fake account storage
            test_fake_account = {
                'account_id': 'test_account_123',
                'platform': 'instagram',
                'username': 'fakeuser',
                'password': 'testpass',
                'email': '<EMAIL>',
                'creation_date': datetime.now().isoformat(),
                'status': 'created'
            }
            
            self.social_media.store_fake_account(test_fake_account)
            print("[✓] Fake account storage test passed")
            
            self.test_results.append({
                'test': 'database_operations',
                'status': 'passed',
                'details': 'All database operations successful'
            })
            
        except Exception as e:
            print(f"[✗] Database operations test error: {e}")
            self.test_results.append({
                'test': 'database_operations',
                'status': 'error',
                'details': str(e)
            })
    
    def test_system_performance(self):
        """Test system performance"""
        try:
            print("[*] Testing system performance...")
            
            # Test multiple operations
            start_time = time.time()
            
            test_profiles = [
                ('facebook', f'user{i}') for i in range(3)
            ]
            
            for platform, username in test_profiles:
                analysis = self.social_media.analyze_profile(platform, username)
                if analysis:
                    fake_account = self.social_media.create_fake_account(platform)
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"[*] Processed {len(test_profiles)} profiles in {execution_time:.2f} seconds")
            print(f"[*] Average time per profile: {execution_time/len(test_profiles):.2f} seconds")
            
            # Check system status
            status = self.social_media.get_system_status()
            stats = status['statistics']
            
            print(f"[*] Total profiles analyzed: {stats['profiles_analyzed']}")
            print(f"[*] Total fake accounts created: {stats['fake_accounts_created']}")
            
            if execution_time < 20:  # Should complete within 20 seconds
                print("[✓] System performance test passed")
                self.test_results.append({
                    'test': 'system_performance',
                    'status': 'passed',
                    'details': f"Execution time: {execution_time:.2f}s"
                })
            else:
                print("[!] System performance slower than expected")
                self.test_results.append({
                    'test': 'system_performance',
                    'status': 'warning',
                    'details': f"Slow execution time: {execution_time:.2f}s"
                })
                
        except Exception as e:
            print(f"[✗] System performance test error: {e}")
            self.test_results.append({
                'test': 'system_performance',
                'status': 'error',
                'details': str(e)
            })
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 SOCIAL MEDIA ACCOUNTS TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['status'] == 'passed')
        failed_tests = sum(1 for result in self.test_results if result['status'] == 'failed')
        error_tests = sum(1 for result in self.test_results if result['status'] == 'error')
        warning_tests = sum(1 for result in self.test_results if result['status'] == 'warning')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ({passed_tests/total_tests*100:.1f}%)")
        print(f"Failed: {failed_tests} ({failed_tests/total_tests*100:.1f}%)")
        print(f"Errors: {error_tests} ({error_tests/total_tests*100:.1f}%)")
        print(f"Warnings: {warning_tests} ({warning_tests/total_tests*100:.1f}%)")
        
        print(f"\nSuccess Rate: {passed_tests/total_tests*100:.1f}%")
        
        print("\nDetailed Results:")
        for result in self.test_results:
            status_icon = {
                'passed': '✓',
                'failed': '✗',
                'error': '⚠',
                'warning': '!'
            }.get(result['status'], '?')
            
            print(f"  [{status_icon}] {result['test']}: {result['details']}")
        
        # Overall assessment
        if passed_tests / total_tests >= 0.8:
            print(f"\n🎉 OVERALL ASSESSMENT: EXCELLENT")
            print("The social media accounts module is working very well!")
        elif passed_tests / total_tests >= 0.6:
            print(f"\n👍 OVERALL ASSESSMENT: GOOD")
            print("The social media accounts module is working adequately.")
        else:
            print(f"\n⚠️ OVERALL ASSESSMENT: NEEDS IMPROVEMENT")
            print("The social media accounts module needs attention.")
        
        print("\n" + "=" * 60)

def main():
    """Main test function"""
    print("🧪 Starting Social Media Accounts Test Suite...")
    
    try:
        tester = SocialMediaAccountsTester()
        tester.run_all_tests()
        
    except KeyboardInterrupt:
        print("\n[!] Tests interrupted by user")
    except Exception as e:
        print(f"\n[✗] Test suite error: {e}")
    finally:
        print("\n[*] Test suite completed")

if __name__ == "__main__":
    main()

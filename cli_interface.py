#!/usr/bin/env python3
"""
Command Line Interface for Social Media Intelligence System
Enhanced CLI with rich formatting and interactive features
"""

import asyncio
import json
import sys
from typing import Dict, List, Optional, Any
from datetime import datetime

try:
    import click
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.prompt import Prompt, Confirm
    from rich.syntax import Syntax
    from rich.tree import Tree
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    print("Rich library not available - using basic CLI")

from social_accounts_standalone import EnhancedSocialMediaIntelligence

class SocialIntelligenceCLI:
    """Enhanced CLI for Social Media Intelligence System"""

    def __init__(self):
        self.console = Console() if RICH_AVAILABLE else None
        self.intelligence_system = None

    def print_banner(self):
        """Print application banner"""
        if RICH_AVAILABLE:
            banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    SOCIAL MEDIA INTELLIGENCE SYSTEM                         ║
║                         Enhanced CLI Interface                              ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🔍 Real OSINT Collection    🤖 AI-Powered Analysis    🔒 Security Features  ║
║  🌐 Multi-Platform Support  📊 Comprehensive Reports  ⚖️  Legal Compliance  ║
╚══════════════════════════════════════════════════════════════════════════════╝
            """
            self.console.print(Panel(banner, style="bold blue"))
        else:
            print("=" * 80)
            print("SOCIAL MEDIA INTELLIGENCE SYSTEM - Enhanced CLI")
            print("=" * 80)

    def print_menu(self):
        """Print main menu"""
        if RICH_AVAILABLE:
            menu_table = Table(title="Main Menu", style="cyan")
            menu_table.add_column("Option", style="bold")
            menu_table.add_column("Description")

            menu_table.add_row("1", "Analyze Profile (Enhanced)")
            menu_table.add_row("2", "OSINT Investigation")
            menu_table.add_row("3", "AI Content Analysis")
            menu_table.add_row("4", "Create Fake Account (Educational)")
            menu_table.add_row("5", "View System Status")
            menu_table.add_row("6", "Security & Audit Reports")
            menu_table.add_row("7", "Configuration")
            menu_table.add_row("8", "Help & Documentation")
            menu_table.add_row("0", "Exit")

            self.console.print(menu_table)
        else:
            print("\nMain Menu:")
            print("1. Analyze Profile (Enhanced)")
            print("2. OSINT Investigation")
            print("3. AI Content Analysis")
            print("4. Create Fake Account (Educational)")
            print("5. View System Status")
            print("6. Security & Audit Reports")
            print("7. Configuration")
            print("8. Help & Documentation")
            print("0. Exit")

    async def analyze_profile_interactive(self):
        """Interactive profile analysis"""
        try:
            if RICH_AVAILABLE:
                self.console.print("\n[bold green]Enhanced Profile Analysis[/bold green]")

                platform = Prompt.ask(
                    "Select platform",
                    choices=["facebook", "instagram", "twitter", "linkedin"],
                    default="instagram"
                )

                username = Prompt.ask("Enter username or profile URL")

                use_real_scraping = Confirm.ask("Use real web scraping?", default=True)
                include_osint = Confirm.ask("Include OSINT investigation?", default=True)
                include_ai = Confirm.ask("Include AI analysis?", default=True)

                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=self.console
                ) as progress:
                    task = progress.add_task("Analyzing profile...", total=None)

                    result = await self.intelligence_system.analyze_profile(
                        platform, username, use_real_scraping, include_osint, include_ai
                    )

                    progress.update(task, completed=True)

                if result and 'error' not in result:
                    self.display_analysis_results(result)
                else:
                    self.console.print(f"[red]Analysis failed: {result.get('error', 'Unknown error')}[/red]")
            else:
                print("\nProfile Analysis")
                platform = input("Platform (facebook/instagram/twitter/linkedin): ")
                username = input("Username: ")

                print("Analyzing profile...")
                result = await self.intelligence_system.analyze_profile(platform, username)

                if result:
                    self.display_analysis_results_basic(result)
                else:
                    print("Analysis failed")

        except Exception as e:
            if RICH_AVAILABLE:
                self.console.print(f"[red]Error: {e}[/red]")
            else:
                print(f"Error: {e}")

    def display_analysis_results(self, result: Dict[str, Any]):
        """Display analysis results with rich formatting"""
        if not RICH_AVAILABLE:
            self.display_analysis_results_basic(result)
            return

        # Main results panel
        main_table = Table(title=f"Analysis Results - {result.get('platform', 'Unknown').title()}")
        main_table.add_column("Metric", style="cyan")
        main_table.add_column("Value", style="green")

        main_table.add_row("Profile ID", result.get('profile_id', 'N/A'))
        main_table.add_row("Username", result.get('username', 'N/A'))
        main_table.add_row("Analysis Type", result.get('analysis_type', 'N/A'))
        main_table.add_row("Methods Used", ', '.join(result.get('methods_used', [])))
        main_table.add_row("Risk Score", f"{result.get('risk_score', 0.0):.2f}")

        self.console.print(main_table)

        # Profile data
        profile_data = result.get('profile_data', {})
        if profile_data:
            profile_table = Table(title="Profile Information")
            profile_table.add_column("Field", style="cyan")
            profile_table.add_column("Value", style="yellow")

            for key, value in profile_data.items():
                if key not in ['error']:
                    profile_table.add_row(key.replace('_', ' ').title(), str(value))

            self.console.print(profile_table)

        # AI Analysis
        ai_analysis = result.get('ai_analysis', {})
        if ai_analysis:
            ai_table = Table(title="AI Analysis Results")
            ai_table.add_column("Analysis Type", style="cyan")
            ai_table.add_column("Results", style="magenta")

            authenticity = ai_analysis.get('authenticity_assessment', {})
            if authenticity:
                ai_table.add_row("Overall Authenticity", f"{authenticity.get('overall_authenticity', 0.0):.2f}")
                ai_table.add_row("Content Authenticity", f"{authenticity.get('content_authenticity', 0.0):.2f}")
                ai_table.add_row("Behavioral Consistency", f"{authenticity.get('behavioral_consistency', 0.0):.2f}")

            self.console.print(ai_table)

        # OSINT Results
        osint_data = result.get('osint_data', {})
        if osint_data and 'comprehensive_report' in osint_data:
            report = osint_data['comprehensive_report']

            osint_table = Table(title="OSINT Investigation Results")
            osint_table.add_column("Category", style="cyan")
            osint_table.add_column("Risk Level", style="red")
            osint_table.add_column("Details", style="white")

            osint_table.add_row("Overall Risk", report.get('overall_risk', 'unknown'), "")

            risk_assessment = report.get('risk_assessment', {})
            for risk_type, level in risk_assessment.items():
                osint_table.add_row(risk_type.replace('_', ' ').title(), level, "")

            self.console.print(osint_table)

            # Recommendations
            recommendations = report.get('recommendations', [])
            if recommendations:
                rec_panel = Panel(
                    '\n'.join(f"• {rec}" for rec in recommendations),
                    title="Security Recommendations",
                    style="yellow"
                )
                self.console.print(rec_panel)

    def display_analysis_results_basic(self, result: Dict[str, Any]):
        """Display analysis results in basic format"""
        print(f"\nAnalysis Results for {result.get('username', 'Unknown')}")
        print("-" * 50)
        print(f"Platform: {result.get('platform', 'N/A')}")
        print(f"Risk Score: {result.get('risk_score', 0.0):.2f}")
        print(f"Methods Used: {', '.join(result.get('methods_used', []))}")

        profile_data = result.get('profile_data', {})
        if profile_data:
            print(f"Followers: {profile_data.get('follower_count', 'N/A')}")
            print(f"Verified: {profile_data.get('verified', 'N/A')}")

        ai_analysis = result.get('ai_analysis', {})
        if ai_analysis:
            authenticity = ai_analysis.get('authenticity_assessment', {})
            print(f"AI Authenticity: {authenticity.get('overall_authenticity', 0.0):.2f}")

    async def osint_investigation(self):
        """OSINT investigation interface"""
        if RICH_AVAILABLE:
            self.console.print("\n[bold blue]OSINT Investigation[/bold blue]")

            investigation_type = Prompt.ask(
                "Investigation type",
                choices=["email", "phone", "username"],
                default="email"
            )

            target = Prompt.ask(f"Enter {investigation_type}")

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Conducting OSINT investigation...", total=None)

                if investigation_type == "email":
                    result = await self.intelligence_system.osint_collector.comprehensive_email_osint(target)
                elif investigation_type == "phone":
                    result = await self.intelligence_system.osint_collector.comprehensive_phone_osint(target)
                else:
                    result = await self.intelligence_system.osint_collector.check_username_availability(target)

                progress.update(task, completed=True)

            # Display results
            if result:
                result_json = Syntax(json.dumps(result, indent=2), "json", theme="monokai")
                self.console.print(Panel(result_json, title="OSINT Results"))
            else:
                self.console.print("[red]Investigation failed[/red]")
        else:
            print("\nOSINT Investigation")
            print("Feature requires rich library for full functionality")

    async def create_fake_account_interactive(self):
        """Interactive fake account creation"""
        if RICH_AVAILABLE:
            self.console.print("\n[bold yellow]Educational Account Creation[/bold yellow]")

            # Show warning
            warning_panel = Panel(
                "⚠️  This feature is for EDUCATIONAL purposes only!\n"
                "Creating fake accounts for malicious purposes is illegal.\n"
                "Only use this for authorized research and testing.",
                title="Legal Warning",
                style="red"
            )
            self.console.print(warning_panel)

            if not Confirm.ask("Do you understand and agree to use this responsibly?"):
                self.console.print("[red]Operation cancelled[/red]")
                return

            platform = Prompt.ask(
                "Select platform",
                choices=["facebook", "instagram", "twitter"],
                default="instagram"
            )

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Creating educational account...", total=None)

                result = await self.intelligence_system.create_fake_account(
                    platform,
                    account_type='educational',
                    educational_mode=True
                )

                progress.update(task, completed=True)

            if result and 'error' not in result:
                account_table = Table(title="Created Account (Educational)")
                account_table.add_column("Field", style="cyan")
                account_table.add_column("Value", style="green")

                account_table.add_row("Platform", result.get('platform', 'N/A'))
                account_table.add_row("Username", result.get('username', 'N/A'))
                account_table.add_row("Status", result.get('status', 'N/A'))
                account_table.add_row("Educational Mode", str(result.get('educational_mode', False)))

                self.console.print(account_table)
            else:
                self.console.print(f"[red]Account creation failed: {result.get('error', 'Unknown error')}[/red]")
        else:
            print("\nEducational Account Creation")
            print("Feature requires rich library for full functionality")

    def show_system_status(self):
        """Show system status"""
        if not self.intelligence_system:
            return

        status = self.intelligence_system.get_system_status()

        if RICH_AVAILABLE:
            # Capabilities table
            cap_table = Table(title="System Capabilities")
            cap_table.add_column("Capability", style="cyan")
            cap_table.add_column("Status", style="green")

            for capability, enabled in self.intelligence_system.capabilities.items():
                status_icon = "✅" if enabled else "❌"
                cap_table.add_row(capability.replace('_', ' ').title(), status_icon)

            self.console.print(cap_table)

            # Statistics table
            stats_table = Table(title="System Statistics")
            stats_table.add_column("Metric", style="cyan")
            stats_table.add_column("Count", style="yellow")

            for metric, count in self.intelligence_system.stats.items():
                stats_table.add_row(metric.replace('_', ' ').title(), str(count))

            self.console.print(stats_table)
        else:
            print("\nSystem Status:")
            print("Capabilities:")
            for capability, enabled in self.intelligence_system.capabilities.items():
                status = "✅" if enabled else "❌"
                print(f"  {status} {capability.replace('_', ' ').title()}")

            print("\nStatistics:")
            for metric, count in self.intelligence_system.stats.items():
                print(f"  {metric.replace('_', ' ').title()}: {count}")

    async def run(self):
        """Run the CLI interface"""
        self.print_banner()

        # Initialize system
        if RICH_AVAILABLE:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Initializing system...", total=None)
                self.intelligence_system = EnhancedSocialMediaIntelligence()
                self.intelligence_system.start_social_media_system()
                progress.update(task, completed=True)
        else:
            print("Initializing system...")
            self.intelligence_system = EnhancedSocialMediaIntelligence()
            self.intelligence_system.start_social_media_system()

        # Main loop
        while True:
            try:
                self.print_menu()

                if RICH_AVAILABLE:
                    choice = Prompt.ask("Select option", default="0")
                else:
                    choice = input("\nSelect option (0-8): ").strip()

                if choice == "0":
                    break
                elif choice == "1":
                    await self.analyze_profile_interactive()
                elif choice == "2":
                    await self.osint_investigation()
                elif choice == "3":
                    if RICH_AVAILABLE:
                        self.console.print("[yellow]AI Content Analysis - Coming Soon[/yellow]")
                    else:
                        print("AI Content Analysis - Coming Soon")
                elif choice == "4":
                    await self.create_fake_account_interactive()
                elif choice == "5":
                    self.show_system_status()
                elif choice == "6":
                    if RICH_AVAILABLE:
                        self.console.print("[yellow]Security Reports - Coming Soon[/yellow]")
                    else:
                        print("Security Reports - Coming Soon")
                elif choice == "7":
                    if RICH_AVAILABLE:
                        self.console.print("[yellow]Configuration - Coming Soon[/yellow]")
                    else:
                        print("Configuration - Coming Soon")
                elif choice == "8":
                    if RICH_AVAILABLE:
                        self.console.print("[yellow]Help Documentation - Coming Soon[/yellow]")
                    else:
                        print("Help Documentation - Coming Soon")
                else:
                    if RICH_AVAILABLE:
                        self.console.print("[red]Invalid option[/red]")
                    else:
                        print("Invalid option")

                if RICH_AVAILABLE:
                    input("\nPress Enter to continue...")
                else:
                    input("\nPress Enter to continue...")

            except KeyboardInterrupt:
                break
            except Exception as e:
                if RICH_AVAILABLE:
                    self.console.print(f"[red]Error: {e}[/red]")
                else:
                    print(f"Error: {e}")

        # Cleanup
        if RICH_AVAILABLE:
            self.console.print("[green]Shutting down system...[/green]")
        else:
            print("Shutting down system...")

        if self.intelligence_system:
            self.intelligence_system.stop_social_media_system()

if __name__ == "__main__":
    cli = SocialIntelligenceCLI()
    asyncio.run(cli.run())

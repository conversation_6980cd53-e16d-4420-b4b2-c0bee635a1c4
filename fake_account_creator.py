#!/usr/bin/env python3
"""
Fake Account Creation Module
Automated account creation with CAPTCHA solving and temporary emails
WARNING: For educational and research purposes only
"""

import time
import json
import random
import string
import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

try:
    import requests
    import aiohttp
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.keys import Keys
    from selenium.common.exceptions import TimeoutException
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    from PIL import Image
    import io
    import base64
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

from config import API_KEYS, PLATFORM_CONFIGS
from web_scraper import RealWebScraper

class FakeAccountCreator:
    """Automated fake account creation system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.scraper = None
        self.session = None
        self.created_accounts = []
        
        # Legal and ethical warnings
        self.show_legal_warning()
        
        if not SELENIUM_AVAILABLE:
            raise ImportError("Selenium is required for account creation")
    
    def show_legal_warning(self):
        """Display legal and ethical warnings"""
        warning_message = """
        ⚠️  LEGAL AND ETHICAL WARNING ⚠️
        
        This tool is designed for:
        - Educational purposes only
        - Security research and testing
        - Authorized penetration testing
        - Academic research with proper approval
        
        PROHIBITED USES:
        - Creating accounts for malicious purposes
        - Impersonation or identity theft
        - Spam or harassment
        - Violation of platform terms of service
        - Any illegal activities
        
        By using this tool, you acknowledge:
        - You have proper authorization
        - You will comply with all applicable laws
        - You understand the ethical implications
        - You will use this responsibly
        
        The developers are not responsible for misuse.
        """
        
        print(warning_message)
        self.logger.warning("Legal warning displayed to user")
    
    async def setup_session(self):
        """Setup HTTP session for API calls"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=30)
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def close_session(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None
    
    def setup_scraper(self, headless: bool = True):
        """Setup web scraper for account creation"""
        if not self.scraper:
            self.scraper = RealWebScraper()
            return self.scraper.setup_driver(headless=headless)
        return True
    
    def generate_fake_identity(self) -> Dict[str, str]:
        """Generate fake identity data"""
        first_names = [
            'James', 'John', 'Robert', 'Michael', 'William', 'David', 'Richard', 'Joseph',
            'Mary', 'Patricia', 'Jennifer', 'Linda', 'Elizabeth', 'Barbara', 'Susan', 'Jessica'
        ]
        
        last_names = [
            'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
            'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson'
        ]
        
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        
        # Generate username variations
        username_patterns = [
            f"{first_name.lower()}{last_name.lower()}",
            f"{first_name.lower()}.{last_name.lower()}",
            f"{first_name.lower()}_{last_name.lower()}",
            f"{first_name.lower()}{random.randint(100, 999)}",
            f"{first_name[0].lower()}{last_name.lower()}{random.randint(10, 99)}"
        ]
        
        username = random.choice(username_patterns)
        
        # Generate other details
        birth_year = random.randint(1980, 2000)
        birth_month = random.randint(1, 12)
        birth_day = random.randint(1, 28)
        
        return {
            'first_name': first_name,
            'last_name': last_name,
            'full_name': f"{first_name} {last_name}",
            'username': username,
            'birth_date': f"{birth_year}-{birth_month:02d}-{birth_day:02d}",
            'birth_year': str(birth_year),
            'birth_month': str(birth_month),
            'birth_day': str(birth_day),
            'gender': random.choice(['male', 'female']),
            'phone': self.generate_fake_phone(),
            'location': random.choice(['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'])
        }
    
    def generate_fake_phone(self) -> str:
        """Generate fake phone number"""
        area_codes = ['555', '123', '456', '789']  # Using obviously fake area codes
        area_code = random.choice(area_codes)
        number = ''.join([str(random.randint(0, 9)) for _ in range(7)])
        return f"+1{area_code}{number}"
    
    def generate_strong_password(self) -> str:
        """Generate strong password"""
        length = random.randint(12, 16)
        characters = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(random.choice(characters) for _ in range(length))
        
        # Ensure password has required character types
        if not any(c.isupper() for c in password):
            password = password[:-1] + random.choice(string.ascii_uppercase)
        if not any(c.islower() for c in password):
            password = password[:-1] + random.choice(string.ascii_lowercase)
        if not any(c.isdigit() for c in password):
            password = password[:-1] + random.choice(string.digits)
        if not any(c in "!@#$%^&*" for c in password):
            password = password[:-1] + random.choice("!@#$%^&*")
        
        return password
    
    async def get_temporary_email(self) -> Dict[str, str]:
        """Get temporary email address"""
        try:
            # Using 10minutemail API (free service)
            await self.setup_session()
            
            # Get temporary email
            async with self.session.get('https://10minutemail.com/10MinuteMail/index.html') as response:
                if response.status == 200:
                    # This is a simplified implementation
                    # In practice, you'd need to parse the HTML or use their API
                    random_email = f"temp{random.randint(10000, 99999)}@10minutemail.com"
                    
                    return {
                        'email': random_email,
                        'service': '10minutemail',
                        'expires_at': (datetime.now() + timedelta(minutes=10)).isoformat(),
                        'access_url': 'https://10minutemail.com'
                    }
            
            # Fallback to guerrilla mail
            async with self.session.get('https://api.guerrillamail.com/ajax.php?f=get_email_address') as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        'email': data.get('email_addr', f"temp{random.randint(10000, 99999)}@guerrillamail.com"),
                        'service': 'guerrillamail',
                        'expires_at': (datetime.now() + timedelta(hours=1)).isoformat(),
                        'sid_token': data.get('sid_token')
                    }
            
            # Final fallback - generate fake email
            domains = ['tempmail.com', 'throwaway.email', 'mailinator.com']
            fake_email = f"temp{random.randint(10000, 99999)}@{random.choice(domains)}"
            
            return {
                'email': fake_email,
                'service': 'generated',
                'expires_at': (datetime.now() + timedelta(hours=1)).isoformat(),
                'note': 'Generated email - may not be functional'
            }
            
        except Exception as e:
            self.logger.error(f"Temporary email generation error: {e}")
            # Fallback to generated email
            domains = ['tempmail.com', 'throwaway.email']
            fake_email = f"temp{random.randint(10000, 99999)}@{random.choice(domains)}"
            
            return {
                'email': fake_email,
                'service': 'fallback',
                'error': str(e)
            }
    
    async def solve_captcha_2captcha(self, captcha_image: str) -> Optional[str]:
        """Solve CAPTCHA using 2captcha service"""
        try:
            if not API_KEYS.get('twocaptcha'):
                self.logger.warning("2captcha API key not configured")
                return None
            
            await self.setup_session()
            
            # Submit CAPTCHA
            submit_url = "http://2captcha.com/in.php"
            submit_data = {
                'method': 'base64',
                'key': API_KEYS['twocaptcha'],
                'body': captcha_image
            }
            
            async with self.session.post(submit_url, data=submit_data) as response:
                result = await response.text()
                
                if result.startswith('OK|'):
                    captcha_id = result.split('|')[1]
                    
                    # Wait for solution
                    for attempt in range(30):  # Wait up to 5 minutes
                        await asyncio.sleep(10)
                        
                        result_url = f"http://2captcha.com/res.php?key={API_KEYS['twocaptcha']}&action=get&id={captcha_id}"
                        async with self.session.get(result_url) as result_response:
                            result_text = await result_response.text()
                            
                            if result_text.startswith('OK|'):
                                return result_text.split('|')[1]
                            elif result_text == 'CAPCHA_NOT_READY':
                                continue
                            else:
                                self.logger.error(f"CAPTCHA solving error: {result_text}")
                                return None
                    
                    self.logger.error("CAPTCHA solving timeout")
                    return None
                else:
                    self.logger.error(f"CAPTCHA submission error: {result}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"2captcha error: {e}")
            return None
    
    def manual_captcha_input(self) -> Optional[str]:
        """Manual CAPTCHA input fallback"""
        try:
            print("\n" + "="*50)
            print("MANUAL CAPTCHA SOLVING REQUIRED")
            print("="*50)
            print("Please solve the CAPTCHA manually in the browser window.")
            print("The browser should be visible for manual interaction.")
            
            # Wait for user to solve CAPTCHA manually
            input("Press Enter after solving the CAPTCHA manually...")
            
            return "manual_solved"
            
        except Exception as e:
            self.logger.error(f"Manual CAPTCHA input error: {e}")
            return None
    
    def human_like_typing(self, element, text: str):
        """Type text with human-like delays"""
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
    
    def random_mouse_movement(self):
        """Simulate random mouse movements"""
        try:
            from selenium.webdriver.common.action_chains import ActionChains
            
            actions = ActionChains(self.scraper.driver)
            
            # Random movements
            for _ in range(random.randint(2, 5)):
                x_offset = random.randint(-100, 100)
                y_offset = random.randint(-100, 100)
                actions.move_by_offset(x_offset, y_offset)
                actions.pause(random.uniform(0.5, 1.5))
            
            actions.perform()
            
        except Exception as e:
            self.logger.debug(f"Mouse movement simulation error: {e}")
    
    async def create_facebook_account(self, identity: Dict[str, str], email_data: Dict[str, str]) -> Dict[str, Any]:
        """Create Facebook account"""
        try:
            self.logger.info(f"Creating Facebook account for {identity['username']}")
            
            if not self.setup_scraper(headless=False):  # Visible for CAPTCHA solving
                return {'error': 'Failed to setup scraper'}
            
            # Navigate to Facebook signup
            self.scraper.driver.get('https://www.facebook.com/reg/')
            self.scraper.human_like_delay(3, 5)
            
            # Fill registration form
            first_name_field = self.scraper.wait_for_element('input[name="firstname"]')
            if first_name_field:
                self.human_like_typing(first_name_field, identity['first_name'])
            
            last_name_field = self.scraper.safe_find_element('input[name="lastname"]')
            if last_name_field:
                self.human_like_typing(last_name_field, identity['last_name'])
            
            email_field = self.scraper.safe_find_element('input[name="reg_email__"]')
            if email_field:
                self.human_like_typing(email_field, email_data['email'])
            
            password_field = self.scraper.safe_find_element('input[name="reg_passwd__"]')
            password = self.generate_strong_password()
            if password_field:
                self.human_like_typing(password_field, password)
            
            # Birth date
            birth_month = self.scraper.safe_find_element('select[name="birthday_month"]')
            if birth_month:
                birth_month.send_keys(identity['birth_month'])
            
            birth_day = self.scraper.safe_find_element('select[name="birthday_day"]')
            if birth_day:
                birth_day.send_keys(identity['birth_day'])
            
            birth_year = self.scraper.safe_find_element('select[name="birthday_year"]')
            if birth_year:
                birth_year.send_keys(identity['birth_year'])
            
            # Gender
            if identity['gender'] == 'male':
                gender_radio = self.scraper.safe_find_element('input[value="2"]')
            else:
                gender_radio = self.scraper.safe_find_element('input[value="1"]')
            
            if gender_radio:
                gender_radio.click()
            
            # Random mouse movements
            self.random_mouse_movement()
            
            # Handle CAPTCHA if present
            captcha_element = self.scraper.safe_find_element('iframe[title*="captcha"]')
            if captcha_element:
                self.logger.info("CAPTCHA detected - attempting to solve")
                
                # Try automated solving first
                captcha_solved = await self.solve_captcha_2captcha("")
                
                if not captcha_solved:
                    # Fall back to manual solving
                    captcha_solved = self.manual_captcha_input()
                
                if not captcha_solved:
                    return {'error': 'CAPTCHA solving failed'}
            
            # Submit form
            submit_button = self.scraper.safe_find_element('button[name="websubmit"]')
            if submit_button:
                submit_button.click()
                self.scraper.human_like_delay(5, 8)
            
            # Check for success or errors
            current_url = self.scraper.driver.current_url
            
            if 'facebook.com' in current_url and 'reg' not in current_url:
                account_data = {
                    'platform': 'facebook',
                    'username': identity['username'],
                    'email': email_data['email'],
                    'password': password,
                    'identity': identity,
                    'created_at': datetime.now().isoformat(),
                    'status': 'created',
                    'profile_url': f"https://facebook.com/{identity['username']}"
                }
                
                self.created_accounts.append(account_data)
                self.logger.info(f"Facebook account created successfully: {identity['username']}")
                return account_data
            else:
                return {'error': 'Account creation failed - check for errors on page'}
                
        except Exception as e:
            self.logger.error(f"Facebook account creation error: {e}")
            return {'error': str(e)}
    
    async def create_account(self, platform: str, educational_mode: bool = True) -> Dict[str, Any]:
        """Main method to create fake account on specified platform"""
        
        if educational_mode:
            print("\n🎓 EDUCATIONAL MODE ENABLED")
            print("This is a demonstration of account creation techniques.")
            print("No actual accounts will be created on real platforms.")
            print("For research and educational purposes only.\n")
        
        try:
            # Generate identity
            identity = self.generate_fake_identity()
            self.logger.info(f"Generated identity: {identity['username']}")
            
            # Get temporary email
            email_data = await self.get_temporary_email()
            self.logger.info(f"Generated email: {email_data['email']}")
            
            if educational_mode:
                # Return simulated data for educational purposes
                return {
                    'platform': platform,
                    'username': identity['username'],
                    'email': email_data['email'],
                    'identity': identity,
                    'created_at': datetime.now().isoformat(),
                    'status': 'simulated',
                    'educational_mode': True,
                    'note': 'This is a simulation for educational purposes'
                }
            
            # Route to platform-specific creator
            if platform == 'facebook':
                return await self.create_facebook_account(identity, email_data)
            else:
                return {'error': f'Platform {platform} not yet implemented'}
                
        except Exception as e:
            self.logger.error(f"Account creation error: {e}")
            return {'error': str(e)}
    
    def get_created_accounts(self) -> List[Dict[str, Any]]:
        """Get list of created accounts"""
        return self.created_accounts
    
    def cleanup(self):
        """Cleanup resources"""
        if self.scraper:
            self.scraper.close()
        
        asyncio.create_task(self.close_session())
    
    async def __aenter__(self):
        await self.setup_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close_session()
